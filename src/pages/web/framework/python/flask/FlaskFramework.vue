<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex flex-col relative">
    <!-- 背景装饰 -->
    <div class="absolute inset-0 overflow-hidden pointer-events-none">
      <div class="absolute -top-40 -right-40 w-80 h-80 bg-blue-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob"></div>
      <div class="absolute -bottom-40 -left-40 w-80 h-80 bg-indigo-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000"></div>
      <div class="absolute -top-40 left-1/2 w-80 h-80 bg-purple-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-4000"></div>
    </div>

    <div class="max-w-7xl mx-auto py-12 px-4 sm:px-6 md:px-8 w-full relative z-10">
      <!-- 返回按钮 -->
      <div class="mb-8">
        <router-link to="/web/framework/python" class="inline-flex items-center text-blue-600 hover:text-blue-700 transition-colors duration-200">
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
          </svg>
          返回 Python 框架
        </router-link>
      </div>

      <!-- 页面头部 -->
      <div class="text-center mb-12">
        <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full mb-6 shadow-lg">
          <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"/>
          </svg>
        </div>
        <h1 class="text-4xl font-extrabold text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-indigo-600 mb-4">
          Flask 框架
        </h1>
        <p class="text-xl text-gray-700 max-w-4xl mx-auto leading-relaxed">
          🚀 深入掌握Flask微框架的完整知识体系 - 从基础概念到安全防护的全方位学习指南
        </p>
        <div class="flex flex-wrap justify-center gap-3 text-sm">
          <span class="bg-blue-100 text-blue-700 px-3 py-1 rounded-full font-medium">微框架</span>
          <span class="bg-indigo-100 text-indigo-700 px-3 py-1 rounded-full font-medium">Jinja2模板</span>
          <span class="bg-purple-100 text-purple-700 px-3 py-1 rounded-full font-medium">蓝图系统</span>
          <span class="bg-pink-100 text-pink-700 px-3 py-1 rounded-full font-medium">扩展生态</span>
          <span class="bg-blue-100 text-blue-700 px-3 py-1 rounded-full font-medium">SSTI防护</span>
          <span class="bg-indigo-100 text-indigo-700 px-3 py-1 rounded-full font-medium">会话安全</span>
        </div>
      </div>

      <!-- 学习模块 -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16">
        <!-- Flask基础知识 -->
        <router-link to="/web/framework/python/flask/basics" class="group">
          <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/50 hover:shadow-2xl transition-all duration-300 group-hover:scale-105 h-64 flex flex-col">
            <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mb-4 group-hover:from-blue-600 group-hover:to-blue-700 transition-all duration-300">
              <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C20.832 18.477 19.246 18 17.5 18c-1.746 0-3.332.477-4.5 1.253"/>
              </svg>
            </div>
            <h3 class="text-xl font-bold text-gray-800 mb-2">Flask 基础知识</h3>
            <p class="text-gray-600 text-sm flex-1">
              Flask核心概念、应用工厂模式、路由系统、视图函数、Jinja2模板引擎等基础知识
            </p>
          </div>
        </router-link>

        <!-- Flask配置文件 -->
        <router-link to="/web/framework/python/flask/config" class="group">
          <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/50 hover:shadow-2xl transition-all duration-300 group-hover:scale-105 h-64 flex flex-col">
            <div class="w-16 h-16 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-xl flex items-center justify-center mb-4 group-hover:from-indigo-600 group-hover:to-indigo-700 transition-all duration-300">
              <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
                <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
              </svg>
            </div>
            <h3 class="text-xl font-bold text-gray-800 mb-2">Flask 配置文件</h3>
            <p class="text-gray-600 text-sm flex-1">
              config.py、instance配置、环境变量等配置文件的详细解析和字段含义
            </p>
          </div>
        </router-link>

        <!-- Flask漏洞分析 -->
        <router-link to="/web/framework/python/flask/vulnerabilities" class="group">
          <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/50 hover:shadow-2xl transition-all duration-300 group-hover:scale-105 h-64 flex flex-col">
            <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center mb-4 group-hover:from-purple-600 group-hover:to-purple-700 transition-all duration-300">
              <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"/>
              </svg>
            </div>
            <h3 class="text-xl font-bold text-gray-800 mb-2">Flask 漏洞分析</h3>
            <p class="text-gray-600 text-sm flex-1">
              SSTI模板注入、Session伪造、Pin码绕过等Flask历史漏洞的分析与利用
            </p>
          </div>
        </router-link>

        <!-- Flask安全加固 -->
        <router-link to="/web/framework/python/flask/hardening" class="group">
          <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/50 hover:shadow-2xl transition-all duration-300 group-hover:scale-105 h-64 flex flex-col">
            <div class="w-16 h-16 bg-gradient-to-br from-pink-500 to-pink-600 rounded-xl flex items-center justify-center mb-4 group-hover:from-pink-600 group-hover:to-pink-700 transition-all duration-300">
              <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"/>
              </svg>
            </div>
            <h3 class="text-xl font-bold text-gray-800 mb-2">Flask 安全加固</h3>
            <p class="text-gray-600 text-sm flex-1">
              Flask应用的安全配置、模板安全、会话保护、扩展安全等安全加固方案
            </p>
          </div>
        </router-link>
      </div>

      <!-- 快速导航 -->
      <div class="bg-white/60 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-white/50">
        <h2 class="text-2xl font-bold text-gray-800 mb-6 text-center">Flask 学习路径</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div class="text-center">
            <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
              <span class="text-blue-600 font-bold">1</span>
            </div>
            <h3 class="font-semibold text-gray-800 mb-2">基础学习</h3>
            <p class="text-sm text-gray-600">掌握Flask核心概念和基础用法</p>
          </div>
          <div class="text-center">
            <div class="w-12 h-12 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-3">
              <span class="text-indigo-600 font-bold">2</span>
            </div>
            <h3 class="font-semibold text-gray-800 mb-2">配置详解</h3>
            <p class="text-sm text-gray-600">深入理解配置文件结构和参数含义</p>
          </div>
          <div class="text-center">
            <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
              <span class="text-purple-600 font-bold">3</span>
            </div>
            <h3 class="font-semibold text-gray-800 mb-2">漏洞研究</h3>
            <p class="text-sm text-gray-600">分析历史漏洞和攻击技术</p>
          </div>
          <div class="text-center">
            <div class="w-12 h-12 bg-pink-100 rounded-full flex items-center justify-center mx-auto mb-3">
              <span class="text-pink-600 font-bold">4</span>
            </div>
            <h3 class="font-semibold text-gray-800 mb-2">安全防护</h3>
            <p class="text-sm text-gray-600">实施全面的安全加固措施</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FlaskFramework'
}
</script>

<style scoped>
@keyframes blob {
  0% { transform: translate(0px, 0px) scale(1); }
  33% { transform: translate(30px, -50px) scale(1.1); }
  66% { transform: translate(-20px, 20px) scale(0.9); }
  100% { transform: translate(0px, 0px) scale(1); }
}

.animate-blob {
  animation: blob 7s infinite;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-4000 {
  animation-delay: 4s;
}
</style>
