<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex flex-col relative">
    <!-- 背景装饰 -->
    <div class="absolute inset-0 overflow-hidden pointer-events-none">
      <div class="absolute -top-40 -right-40 w-80 h-80 bg-blue-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob"></div>
      <div class="absolute -bottom-40 -left-40 w-80 h-80 bg-indigo-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000"></div>
    </div>

    <div class="max-w-7xl mx-auto py-12 px-4 sm:px-6 md:px-8 w-full relative z-10">
      <!-- 返回按钮 -->
      <div class="mb-8">
        <router-link to="/web/framework/python/flask" class="inline-flex items-center text-blue-600 hover:text-blue-700 transition-colors duration-200">
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
          </svg>
          返回 Flask 框架
        </router-link>
      </div>

      <!-- 页面头部 -->
      <div class="text-center mb-12">
        <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full mb-6 shadow-lg">
          <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"/>
          </svg>
        </div>
        <h1 class="text-4xl font-extrabold text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-indigo-600 mb-4">
          Flask 安全加固
        </h1>
        <p class="text-xl text-gray-700 max-w-4xl mx-auto leading-relaxed">
          🛡️ 全面的Flask应用安全加固方案和最佳实践
        </p>
      </div>

      <!-- 基础安全配置 -->
      <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-white/50 mb-8">
        <h2 class="text-3xl font-bold text-blue-800 mb-6 flex items-center">
          <svg class="w-8 h-8 mr-3 text-blue-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
            <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
          </svg>
          基础安全配置
        </h2>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-bold text-blue-800 mb-4">生产环境配置</h3>
            <div class="space-y-4">
              <div class="bg-red-50 p-4 rounded-lg border border-red-200">
                <h4 class="font-bold text-red-700 mb-2">关闭调试模式</h4>
                <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code># config.py - 生产环境配置
import os
from datetime import timedelta

class ProductionConfig:
    # 关闭调试模式
    DEBUG = False
    TESTING = False
    
    # 强制使用环境变量中的SECRET_KEY
    SECRET_KEY = os.environ.get('SECRET_KEY')
    if not SECRET_KEY:
        raise ValueError("No SECRET_KEY set for production")
    
    # 数据库配置
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL')
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_RECORD_QUERIES = False
    
    # 会话安全配置
    SESSION_COOKIE_SECURE = True
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'
    PERMANENT_SESSION_LIFETIME = timedelta(hours=1)
    
    # HTTPS配置
    PREFERRED_URL_SCHEME = 'https'
    
    # 移除敏感信息
    SEND_FILE_MAX_AGE_DEFAULT = timedelta(hours=1)
    
    # 错误处理
    PROPAGATE_EXCEPTIONS = False

# 应用工厂中的安全配置
def create_app(config_name='production'):
    app = Flask(__name__)
    app.config.from_object(config[config_name])
    
    # 移除默认错误处理器
    if not app.debug:
        app.errorhandler(404)(lambda e: ('Not Found', 404))
        app.errorhandler(500)(lambda e: ('Internal Server Error', 500))
    
    return app</code></pre>
              </div>

              <div class="bg-blue-50 p-4 rounded-lg border border-blue-200">
                <h4 class="font-bold text-blue-700 mb-2">安全头配置</h4>
                <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code># 安全头中间件
from flask import Flask, request, g

class SecurityHeadersMiddleware:
    def __init__(self, app):
        self.app = app
        self.init_app(app)
    
    def init_app(self, app):
        app.after_request(self.add_security_headers)
    
    def add_security_headers(self, response):
        # 防止XSS攻击
        response.headers['X-Content-Type-Options'] = 'nosniff'
        response.headers['X-Frame-Options'] = 'DENY'
        response.headers['X-XSS-Protection'] = '1; mode=block'
        
        # 内容安全策略
        response.headers['Content-Security-Policy'] = (
            "default-src 'self'; "
            "script-src 'self' 'unsafe-inline'; "
            "style-src 'self' 'unsafe-inline'; "
            "img-src 'self' data: https:; "
            "font-src 'self'; "
            "connect-src 'self'; "
            "frame-ancestors 'none';"
        )
        
        # HSTS
        if request.is_secure:
            response.headers['Strict-Transport-Security'] = (
                'max-age=31536000; includeSubDomains; preload'
            )
        
        # 引用策略
        response.headers['Referrer-Policy'] = 'strict-origin-when-cross-origin'
        
        # 权限策略
        response.headers['Permissions-Policy'] = (
            'geolocation=(), microphone=(), camera=()'
        )
        
        return response

# 在应用中使用
app = Flask(__name__)
SecurityHeadersMiddleware(app)</code></pre>
              </div>
            </div>
          </div>

          <div>
            <h3 class="text-xl font-bold text-blue-800 mb-4">密钥管理</h3>
            <div class="space-y-4">
              <div class="bg-green-50 p-4 rounded-lg border border-green-200">
                <h4 class="font-bold text-green-700 mb-2">安全的密钥生成</h4>
                <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code># 安全的SECRET_KEY生成
import secrets
import os
from cryptography.fernet import Fernet

def generate_secret_key():
    """生成安全的SECRET_KEY"""
    return secrets.token_urlsafe(32)

def generate_fernet_key():
    """生成Fernet加密密钥"""
    return Fernet.generate_key()

# 密钥轮换机制
class KeyManager:
    def __init__(self, app=None):
        self.app = app
        if app:
            self.init_app(app)
    
    def init_app(self, app):
        # 支持多个密钥用于轮换
        self.keys = [
            app.config.get('SECRET_KEY'),
            app.config.get('SECRET_KEY_OLD'),
        ]
        self.keys = [k for k in self.keys if k]  # 过滤None值
    
    def sign_data(self, data):
        """使用最新密钥签名"""
        from itsdangerous import URLSafeTimedSerializer
        s = URLSafeTimedSerializer(self.keys[0])
        return s.dumps(data)
    
    def verify_data(self, signed_data, max_age=3600):
        """验证签名，支持旧密钥"""
        from itsdangerous import URLSafeTimedSerializer, BadSignature
        
        for key in self.keys:
            try:
                s = URLSafeTimedSerializer(key)
                return s.loads(signed_data, max_age=max_age)
            except BadSignature:
                continue
        
        raise BadSignature('Invalid signature')

# 环境变量配置
# .env文件
SECRET_KEY=your-super-secure-secret-key-here
SECRET_KEY_OLD=your-old-secret-key-for-rotation
JWT_SECRET_KEY=your-jwt-secret-key
ENCRYPTION_KEY=your-encryption-key</code></pre>
              </div>

              <div class="bg-purple-50 p-4 rounded-lg border border-purple-200">
                <h4 class="font-bold text-purple-700 mb-2">敏感数据加密</h4>
                <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code># 敏感数据加密工具
from cryptography.fernet import Fernet
import base64
import os

class DataEncryption:
    def __init__(self, key=None):
        if key is None:
            key = os.environ.get('ENCRYPTION_KEY')
        
        if isinstance(key, str):
            key = key.encode()
        
        self.fernet = Fernet(key)
    
    def encrypt(self, data):
        """加密数据"""
        if isinstance(data, str):
            data = data.encode()
        
        encrypted = self.fernet.encrypt(data)
        return base64.urlsafe_b64encode(encrypted).decode()
    
    def decrypt(self, encrypted_data):
        """解密数据"""
        encrypted_data = base64.urlsafe_b64decode(encrypted_data.encode())
        decrypted = self.fernet.decrypt(encrypted_data)
        return decrypted.decode()

# 在模型中使用加密
from sqlalchemy_utils import EncryptedType
from sqlalchemy_utils.types.encrypted.encrypted_type import AesEngine

class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    
    # 加密敏感字段
    email = db.Column(EncryptedType(db.String, secret_key, AesEngine, 'pkcs5'))
    phone = db.Column(EncryptedType(db.String, secret_key, AesEngine, 'pkcs5'))
    
    # 密码哈希
    password_hash = db.Column(db.String(128))
    
    def set_password(self, password):
        from werkzeug.security import generate_password_hash
        self.password_hash = generate_password_hash(password, method='pbkdf2:sha256', salt_length=16)
    
    def check_password(self, password):
        from werkzeug.security import check_password_hash
        return check_password_hash(self.password_hash, password)</code></pre>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 模板安全防护 -->
      <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-white/50 mb-8">
        <h2 class="text-3xl font-bold text-indigo-800 mb-6 flex items-center">
          <svg class="w-8 h-8 mr-3 text-indigo-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"/>
          </svg>
          模板安全防护
        </h2>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-bold text-indigo-800 mb-4">SSTI防护</h3>
            <div class="space-y-4">
              <div class="bg-red-50 p-3 rounded border border-red-200">
                <strong class="text-red-700">避免动态模板渲染：</strong>
                <pre class="bg-white p-2 rounded mt-2 text-xs"><code># 危险的做法 - 避免使用
from flask import render_template_string

@app.route('/hello')
def hello():
    name = request.args.get('name', 'World')
    # 危险：直接使用用户输入作为模板
    template = f"Hello &#123;&#123; {name} &#125;&#125;!"
    return render_template_string(template)

# 安全的做法
@app.route('/hello')
def hello():
    name = request.args.get('name', 'World')
    # 安全：使用预定义模板，用户输入作为变量
    return render_template('hello.html', name=name)

# hello.html
&lt;h1&gt;Hello &#123;&#123; name|e &#125;&#125;!&lt;/h1&gt;</code></pre>
              </div>

              <div class="bg-blue-50 p-3 rounded border border-blue-200">
                <strong class="text-blue-700">输入验证和过滤：</strong>
                <pre class="bg-white p-2 rounded mt-2 text-xs"><code># 输入验证装饰器
import re
from functools import wraps
from flask import request, abort

def validate_input(pattern=None, max_length=100):
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            for key, value in request.args.items():
                # 长度检查
                if len(value) > max_length:
                    abort(400, f"Parameter {key} too long")
                
                # 模式检查
                if pattern and not re.match(pattern, value):
                    abort(400, f"Invalid parameter {key}")
                
                # 危险字符检查
                dangerous_chars = ['{{', '}}', '{%', '%}', '__', 'config', 'self']
                for char in dangerous_chars:
                    if char in value:
                        abort(400, f"Dangerous character in {key}")
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

# 使用验证装饰器
@app.route('/search')
@validate_input(pattern=r'^[a-zA-Z0-9\s]+$', max_length=50)
def search():
    query = request.args.get('q', '')
    return render_template('search.html', query=query)</code></pre>
              </div>
            </div>
          </div>

          <div>
            <h3 class="text-xl font-bold text-indigo-800 mb-4">安全的模板配置</h3>
            <div class="space-y-4">
              <div class="bg-green-50 p-3 rounded border border-green-200">
                <strong class="text-green-700">Jinja2安全配置：</strong>
                <pre class="bg-white p-2 rounded mt-2 text-xs"><code># 安全的Jinja2配置
from flask import Flask
from jinja2 import Environment, select_autoescape

app = Flask(__name__)

# 配置Jinja2环境
app.jinja_env.autoescape = select_autoescape(['html', 'xml'])

# 移除危险的全局函数
dangerous_globals = [
    'range', 'dict', 'list', 'tuple', 'set',
    'getattr', 'setattr', 'hasattr', 'delattr',
    '__import__', 'eval', 'exec', 'compile',
    'open', 'file', 'input', 'raw_input'
]

for name in dangerous_globals:
    if name in app.jinja_env.globals:
        del app.jinja_env.globals[name]

# 自定义安全过滤器
@app.template_filter('safe_html')
def safe_html_filter(text):
    """安全的HTML过滤器"""
    import bleach
    
    allowed_tags = ['p', 'br', 'strong', 'em', 'ul', 'ol', 'li', 'a']
    allowed_attributes = {'a': ['href', 'title']}
    
    return bleach.clean(text, tags=allowed_tags, attributes=allowed_attributes)

# 在模板中使用
# &#123;&#123; user_content|safe_html|safe &#125;&#125;</code></pre>
              </div>

              <div class="bg-yellow-50 p-3 rounded border border-yellow-200">
                <strong class="text-yellow-700">模板沙箱配置：</strong>
                <pre class="bg-white p-2 rounded mt-2 text-xs"><code># 自定义安全的Jinja2环境
from jinja2.sandbox import SandboxedEnvironment
from jinja2 import select_autoescape

class SecureJinjaEnvironment(SandboxedEnvironment):
    """安全的Jinja2环境"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # 启用自动转义
        self.autoescape = select_autoescape(['html', 'xml'])
        
        # 限制可访问的属性
        self.allowed_attributes = {
            'user': ['username', 'email', 'id'],
            'post': ['title', 'content', 'created_at'],
        }
    
    def is_safe_attribute(self, obj, attr, value):
        """检查属性访问是否安全"""
        obj_type = type(obj).__name__.lower()
        
        if obj_type in self.allowed_attributes:
            return attr in self.allowed_attributes[obj_type]
        
        # 默认拒绝访问
        return False

# 在Flask应用中使用
def create_app():
    app = Flask(__name__)
    
    # 使用安全的Jinja2环境
    app.jinja_env = SecureJinjaEnvironment(
        loader=app.jinja_loader,
        autoescape=True
    )
    
    return app</code></pre>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 会话安全加固 -->
      <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-white/50 mb-8">
        <h2 class="text-3xl font-bold text-purple-800 mb-6 flex items-center">
          <svg class="w-8 h-8 mr-3 text-purple-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
          </svg>
          会话安全加固
        </h2>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-bold text-purple-800 mb-4">安全的Session配置</h3>
            <div class="space-y-4">
              <div class="bg-purple-50 p-4 rounded-lg border border-purple-200">
                <h4 class="font-bold text-purple-700 mb-2">Session安全配置</h4>
                <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code># config.py - Session安全配置
from datetime import timedelta
import os

class Config:
    # 强制使用强密钥
    SECRET_KEY = os.environ.get('SECRET_KEY')
    if not SECRET_KEY or len(SECRET_KEY) < 32:
        raise ValueError("SECRET_KEY must be at least 32 characters long")

    # Session安全配置
    SESSION_COOKIE_NAME = 'session'
    SESSION_COOKIE_DOMAIN = None  # 限制域名
    SESSION_COOKIE_PATH = '/'
    SESSION_COOKIE_HTTPONLY = True  # 防止XSS
    SESSION_COOKIE_SECURE = True    # 仅HTTPS
    SESSION_COOKIE_SAMESITE = 'Lax' # 防止CSRF

    # Session生存时间
    PERMANENT_SESSION_LIFETIME = timedelta(hours=2)

    # 使用服务器端Session存储
    SESSION_TYPE = 'redis'
    SESSION_REDIS = redis.from_url(os.environ.get('REDIS_URL', 'redis://localhost:6379'))
    SESSION_USE_SIGNER = True
    SESSION_KEY_PREFIX = 'flask_session:'
    SESSION_PERMANENT = False

# 自定义Session接口
from flask.sessions import SessionInterface, SessionMixin
import redis
import pickle
import uuid

class SecureRedisSession(dict, SessionMixin):
    def __init__(self, initial=None, sid=None):
        self.sid = sid
        self.modified = False
        super().__init__(initial or ())

class SecureRedisSessionInterface(SessionInterface):
    def __init__(self, redis_client, key_prefix='session:'):
        self.redis = redis_client
        self.key_prefix = key_prefix

    def generate_sid(self):
        return str(uuid.uuid4())

    def get_redis_key(self, sid):
        return self.key_prefix + sid

    def open_session(self, app, request):
        sid = request.cookies.get(app.session_cookie_name)
        if not sid:
            sid = self.generate_sid()
            return SecureRedisSession(sid=sid)

        try:
            data = self.redis.get(self.get_redis_key(sid))
            if data:
                return SecureRedisSession(pickle.loads(data), sid=sid)
        except:
            pass

        sid = self.generate_sid()
        return SecureRedisSession(sid=sid)

    def save_session(self, app, session, response):
        if not session:
            return

        redis_key = self.get_redis_key(session.sid)

        if session.modified:
            # 设置过期时间
            ttl = app.permanent_session_lifetime.total_seconds()
            self.redis.setex(redis_key, int(ttl), pickle.dumps(dict(session)))

        # 设置Cookie
        response.set_cookie(
            app.session_cookie_name,
            session.sid,
            expires=self.get_expiration_time(app, session),
            httponly=app.config['SESSION_COOKIE_HTTPONLY'],
            secure=app.config['SESSION_COOKIE_SECURE'],
            samesite=app.config['SESSION_COOKIE_SAMESITE']
        )</code></pre>
              </div>
            </div>
          </div>

          <div>
            <h3 class="text-xl font-bold text-purple-800 mb-4">Session劫持防护</h3>
            <div class="space-y-4">
              <div class="bg-blue-50 p-4 rounded-lg border border-blue-200">
                <h4 class="font-bold text-blue-700 mb-2">Session验证机制</h4>
                <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code># Session安全验证
from flask import session, request, g
import hashlib

class SessionSecurity:
    def __init__(self, app=None):
        self.app = app
        if app:
            self.init_app(app)

    def init_app(self, app):
        app.before_request(self.validate_session)
        app.after_request(self.update_session_security)

    def get_client_fingerprint(self, request):
        """生成客户端指纹"""
        user_agent = request.headers.get('User-Agent', '')
        accept_language = request.headers.get('Accept-Language', '')
        accept_encoding = request.headers.get('Accept-Encoding', '')

        fingerprint_data = f"{user_agent}:{accept_language}:{accept_encoding}"
        return hashlib.sha256(fingerprint_data.encode()).hexdigest()[:16]

    def validate_session(self):
        """验证Session安全性"""
        if 'user_id' in session:
            # 检查客户端指纹
            current_fingerprint = self.get_client_fingerprint(request)
            stored_fingerprint = session.get('_fingerprint')

            if stored_fingerprint and stored_fingerprint != current_fingerprint:
                session.clear()
                return

            # 检查IP地址变化
            current_ip = request.remote_addr
            stored_ip = session.get('_ip_address')

            if stored_ip and stored_ip != current_ip:
                # IP变化，需要重新验证
                session['_ip_changed'] = True

            # 检查Session年龄
            import time
            current_time = time.time()
            session_start = session.get('_session_start', current_time)

            if current_time - session_start > 3600:  # 1小时
                session.clear()
                return

    def update_session_security(self, response):
        """更新Session安全信息"""
        if 'user_id' in session:
            session['_fingerprint'] = self.get_client_fingerprint(request)
            session['_ip_address'] = request.remote_addr
            session['_last_activity'] = time.time()

            if '_session_start' not in session:
                session['_session_start'] = time.time()

        return response

# 使用Session安全验证
app = Flask(__name__)
SessionSecurity(app)</code></pre>
              </div>

              <div class="bg-green-50 p-4 rounded-lg border border-green-200">
                <h4 class="font-bold text-green-700 mb-2">Session轮换机制</h4>
                <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code># Session ID轮换
from flask import session
import uuid

def regenerate_session_id():
    """重新生成Session ID"""
    # 保存当前Session数据
    session_data = dict(session)

    # 清除当前Session
    session.clear()

    # 恢复Session数据
    session.update(session_data)

    # 标记Session已修改
    session.modified = True

# 在关键操作后轮换Session ID
@app.route('/login', methods=['POST'])
def login():
    username = request.form['username']
    password = request.form['password']

    user = authenticate(username, password)
    if user:
        # 登录成功后轮换Session ID
        regenerate_session_id()

        session['user_id'] = user.id
        session['username'] = user.username
        session['login_time'] = time.time()

        return redirect(url_for('dashboard'))

    return render_template('login.html', error='Invalid credentials')

@app.route('/change_password', methods=['POST'])
def change_password():
    # 密码修改后轮换Session ID
    if update_password(current_user, request.form['new_password']):
        regenerate_session_id()
        flash('Password updated successfully')

    return redirect(url_for('profile'))

# 定期轮换Session ID
@app.before_request
def periodic_session_rotation():
    if 'user_id' in session:
        last_rotation = session.get('_last_rotation', 0)
        current_time = time.time()

        # 每30分钟轮换一次
        if current_time - last_rotation > 1800:
            regenerate_session_id()
            session['_last_rotation'] = current_time</code></pre>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 输入验证和输出编码 -->
      <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-white/50 mb-8">
        <h2 class="text-3xl font-bold text-pink-800 mb-6 flex items-center">
          <svg class="w-8 h-8 mr-3 text-pink-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
          </svg>
          输入验证和输出编码
        </h2>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-bold text-pink-800 mb-4">输入验证</h3>
            <div class="space-y-4">
              <div class="bg-pink-50 p-4 rounded-lg border border-pink-200">
                <h4 class="font-bold text-pink-700 mb-2">表单验证</h4>
                <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code># 使用Flask-WTF进行表单验证
from flask_wtf import FlaskForm
from wtforms import StringField, PasswordField, TextAreaField
from wtforms.validators import DataRequired, Length, Email, Regexp
from wtforms.widgets import TextArea

class SecureTextAreaWidget(TextArea):
    """安全的文本区域组件"""
    def __call__(self, field, **kwargs):
        # 移除危险属性
        dangerous_attrs = ['onclick', 'onload', 'onerror', 'onmouseover']
        for attr in dangerous_attrs:
            kwargs.pop(attr, None)

        return super().__call__(field, **kwargs)

class ContactForm(FlaskForm):
    name = StringField('姓名', validators=[
        DataRequired(message='姓名不能为空'),
        Length(min=2, max=50, message='姓名长度必须在2-50字符之间'),
        Regexp(r'^[a-zA-Z\u4e00-\u9fa5\s]+$', message='姓名只能包含字母、汉字和空格')
    ])

    email = StringField('邮箱', validators=[
        DataRequired(message='邮箱不能为空'),
        Email(message='请输入有效的邮箱地址'),
        Length(max=100, message='邮箱长度不能超过100字符')
    ])

    message = TextAreaField('消息',
        widget=SecureTextAreaWidget(),
        validators=[
            DataRequired(message='消息不能为空'),
            Length(min=10, max=1000, message='消息长度必须在10-1000字符之间')
        ]
    )

    def validate_message(self, field):
        """自定义消息验证"""
        dangerous_patterns = [
            r'&lt;script.*?&gt;',
            r'javascript:',
            r'vbscript:',
            r'on\w+\s*=',
            r'&#123;&#123;.*?&#125;&#125;',
            r'&#123;%.*?%&#125;'
        ]

        import re
        for pattern in dangerous_patterns:
            if re.search(pattern, field.data, re.IGNORECASE):
                raise ValidationError('消息包含不安全的内容')

# 自定义验证器
from wtforms.validators import ValidationError

def no_html_tags(form, field):
    """禁止HTML标签验证器"""
    import re
    if re.search(r'&lt;[^&gt;]+&gt;', field.data):
        raise ValidationError('不允许使用HTML标签')

def safe_filename(form, field):
    """安全文件名验证器"""
    import re
    if not re.match(r'^[a-zA-Z0-9._-]+$', field.data):
        raise ValidationError('文件名只能包含字母、数字、点、下划线和连字符')</code></pre>
              </div>
            </div>
          </div>

          <div>
            <h3 class="text-xl font-bold text-pink-800 mb-4">输出编码</h3>
            <div class="space-y-4">
              <div class="bg-blue-50 p-4 rounded-lg border border-blue-200">
                <h4 class="font-bold text-blue-700 mb-2">安全的输出处理</h4>
                <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code># 输出编码和过滤
from markupsafe import Markup, escape
import bleach
import html

class OutputSecurity:
    @staticmethod
    def escape_html(text):
        """HTML转义"""
        if text is None:
            return ''
        return escape(text)

    @staticmethod
    def clean_html(text, allowed_tags=None, allowed_attributes=None):
        """清理HTML内容"""
        if allowed_tags is None:
            allowed_tags = ['p', 'br', 'strong', 'em', 'ul', 'ol', 'li']

        if allowed_attributes is None:
            allowed_attributes = {}

        return bleach.clean(text, tags=allowed_tags, attributes=allowed_attributes)

    @staticmethod
    def safe_json(data):
        """安全的JSON输出"""
        import json
        # 防止XSS攻击
        json_str = json.dumps(data, ensure_ascii=True)
        # 转义危险字符
        json_str = json_str.replace('&lt;', '\\u003c')
        json_str = json_str.replace('&gt;', '\\u003e')
        json_str = json_str.replace('&amp;', '\\u0026')
        return json_str

# 自定义Jinja2过滤器
@app.template_filter('safe_html')
def safe_html_filter(text):
    """安全HTML过滤器"""
    return Markup(OutputSecurity.clean_html(text))

@app.template_filter('escape_js')
def escape_js_filter(text):
    """JavaScript转义过滤器"""
    if text is None:
        return ''

    # JavaScript字符转义
    escape_map = {
        '\\': '\\\\',
        '"': '\\"',
        "'": "\\'",
        '\n': '\\n',
        '\r': '\\r',
        '\t': '\\t',
        '\b': '\\b',
        '\f': '\\f',
        '/': '\\/',
        '&lt;': '\\u003c',
        '&gt;': '\\u003e',
        '&amp;': '\\u0026'
    }

    for char, escaped in escape_map.items():
        text = text.replace(char, escaped)

    return text

# 在模板中使用
# &#123;&#123; user_content|safe_html &#125;&#125;
# &lt;script&gt;var data = "&#123;&#123; json_data|escape_js &#125;&#125;";&lt;/script&gt;</code></pre>
              </div>

              <div class="bg-green-50 p-4 rounded-lg border border-green-200">
                <h4 class="font-bold text-green-700 mb-2">内容安全策略</h4>
                <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code># 内容安全策略(CSP)实现
from flask import request, g
import hashlib
import base64

class CSPManager:
    def __init__(self, app=None):
        self.app = app
        self.nonces = {}
        if app:
            self.init_app(app)

    def init_app(self, app):
        app.before_request(self.generate_nonce)
        app.after_request(self.add_csp_header)

    def generate_nonce(self):
        """生成随机nonce"""
        import secrets
        nonce = base64.b64encode(secrets.token_bytes(16)).decode('ascii')
        g.csp_nonce = nonce

    def add_csp_header(self, response):
        """添加CSP头"""
        nonce = getattr(g, 'csp_nonce', '')

        csp_policy = (
            f"default-src 'self'; "
            f"script-src 'self' 'nonce-{nonce}' 'strict-dynamic'; "
            f"style-src 'self' 'nonce-{nonce}' 'unsafe-inline'; "
            f"img-src 'self' data: https:; "
            f"font-src 'self'; "
            f"connect-src 'self'; "
            f"frame-ancestors 'none'; "
            f"base-uri 'self'; "
            f"form-action 'self';"
        )

        response.headers['Content-Security-Policy'] = csp_policy
        return response

# 在模板中使用nonce
# &lt;script nonce="&#123;&#123; g.csp_nonce &#125;&#125;"&gt;
#   // 安全的内联脚本
# &lt;/script&gt;

# 使用CSP管理器
app = Flask(__name__)
CSPManager(app)</code></pre>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 日志和监控 -->
      <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-white/50 mb-8">
        <h2 class="text-3xl font-bold text-cyan-800 mb-6 flex items-center">
          <svg class="w-8 h-8 mr-3 text-cyan-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
          </svg>
          安全日志和监控
        </h2>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-bold text-cyan-800 mb-4">安全事件日志</h3>
            <div class="space-y-4">
              <div class="bg-cyan-50 p-4 rounded-lg border border-cyan-200">
                <h4 class="font-bold text-cyan-700 mb-2">日志配置</h4>
                <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code># 安全日志配置
import logging
from logging.handlers import RotatingFileHandler
import json
from datetime import datetime

class SecurityLogger:
    def __init__(self, app=None):
        self.app = app
        if app:
            self.init_app(app)

    def init_app(self, app):
        # 配置安全日志
        security_handler = RotatingFileHandler(
            'logs/security.log',
            maxBytes=10240000,  # 10MB
            backupCount=10
        )

        security_formatter = logging.Formatter(
            '%(asctime)s %(levelname)s %(name)s %(message)s'
        )
        security_handler.setFormatter(security_formatter)

        # 创建安全日志记录器
        self.security_logger = logging.getLogger('security')
        self.security_logger.setLevel(logging.WARNING)
        self.security_logger.addHandler(security_handler)

        # 注册请求钩子
        app.before_request(self.log_request)
        app.after_request(self.log_response)

    def log_security_event(self, event_type, details, level=logging.WARNING):
        """记录安全事件"""
        event_data = {
            'timestamp': datetime.utcnow().isoformat(),
            'event_type': event_type,
            'ip_address': request.remote_addr,
            'user_agent': request.headers.get('User-Agent', ''),
            'user_id': getattr(g, 'user_id', None),
            'details': details
        }

        self.security_logger.log(level, json.dumps(event_data))

    def log_request(self):
        """记录可疑请求"""
        # 检查可疑的请求参数
        suspicious_patterns = [
            r'&#123;&#123;.*?&#125;&#125;',  # SSTI
            r'&lt;script.*?&gt;',        # XSS
            r'union.*select',      # SQL注入
            r'\.\./',              # 路径遍历
            r'etc/passwd',         # 文件读取
        ]

        import re
        request_data = str(request.args) + str(request.form) + str(request.json or '')

        for pattern in suspicious_patterns:
            if re.search(pattern, request_data, re.IGNORECASE):
                self.log_security_event(
                    'suspicious_request',
                    {
                        'pattern': pattern,
                        'url': request.url,
                        'method': request.method,
                        'data': request_data[:500]  # 限制长度
                    }
                )
                break

# 使用安全日志
app = Flask(__name__)
SecurityLogger(app)</code></pre>
              </div>
            </div>
          </div>

          <div>
            <h3 class="text-xl font-bold text-cyan-800 mb-4">实时监控</h3>
            <div class="space-y-4">
              <div class="bg-blue-50 p-4 rounded-lg border border-blue-200">
                <h4 class="font-bold text-blue-700 mb-2">异常检测</h4>
                <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code># 实时安全监控
from collections import defaultdict, deque
import time
from threading import Lock

class SecurityMonitor:
    def __init__(self, app=None):
        self.app = app
        self.request_counts = defaultdict(deque)
        self.failed_logins = defaultdict(deque)
        self.lock = Lock()

        if app:
            self.init_app(app)

    def init_app(self, app):
        app.before_request(self.monitor_request)
        app.after_request(self.monitor_response)

    def monitor_request(self):
        """监控请求频率"""
        client_ip = request.remote_addr
        current_time = time.time()

        with self.lock:
            # 清理过期记录
            cutoff_time = current_time - 60  # 1分钟窗口
            while (self.request_counts[client_ip] and
                   self.request_counts[client_ip][0] < cutoff_time):
                self.request_counts[client_ip].popleft()

            # 添加当前请求
            self.request_counts[client_ip].append(current_time)

            # 检查请求频率
            if len(self.request_counts[client_ip]) > 100:  # 每分钟超过100次请求
                self.handle_rate_limit(client_ip)

    def handle_rate_limit(self, client_ip):
        """处理频率限制"""
        from flask import abort

        # 记录安全事件
        security_logger.log_security_event(
            'rate_limit_exceeded',
            {'ip': client_ip, 'requests_per_minute': len(self.request_counts[client_ip])}
        )

        # 可以选择阻止请求或记录警告
        abort(429, 'Too Many Requests')

    def monitor_failed_login(self, username, ip_address):
        """监控登录失败"""
        current_time = time.time()
        key = f"{username}:{ip_address}"

        with self.lock:
            # 清理过期记录
            cutoff_time = current_time - 300  # 5分钟窗口
            while (self.failed_logins[key] and
                   self.failed_logins[key][0] < cutoff_time):
                self.failed_logins[key].popleft()

            # 添加失败记录
            self.failed_logins[key].append(current_time)

            # 检查失败次数
            if len(self.failed_logins[key]) >= 5:  # 5次失败
                self.handle_brute_force(username, ip_address)

    def handle_brute_force(self, username, ip_address):
        """处理暴力破解"""
        security_logger.log_security_event(
            'brute_force_detected',
            {
                'username': username,
                'ip_address': ip_address,
                'failed_attempts': len(self.failed_logins[f"{username}:{ip_address}"])
            }
        )

        # 可以实施账户锁定或IP封禁

# 使用安全监控
app = Flask(__name__)
security_monitor = SecurityMonitor(app)</code></pre>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FlaskHardening'
}
</script>

<style scoped>
@keyframes blob {
  0% { transform: translate(0px, 0px) scale(1); }
  33% { transform: translate(30px, -50px) scale(1.1); }
  66% { transform: translate(-20px, 20px) scale(0.9); }
  100% { transform: translate(0px, 0px) scale(1); }
}

.animate-blob {
  animation: blob 7s infinite;
}

.animation-delay-2000 {
  animation-delay: 2s;
}
</style>
