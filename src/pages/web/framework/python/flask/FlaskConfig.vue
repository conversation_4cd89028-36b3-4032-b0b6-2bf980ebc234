<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex flex-col relative">
    <!-- 背景装饰 -->
    <div class="absolute inset-0 overflow-hidden pointer-events-none">
      <div class="absolute -top-40 -right-40 w-80 h-80 bg-blue-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob"></div>
      <div class="absolute -bottom-40 -left-40 w-80 h-80 bg-indigo-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000"></div>
    </div>

    <div class="max-w-7xl mx-auto py-12 px-4 sm:px-6 md:px-8 w-full relative z-10">
      <!-- 返回按钮 -->
      <div class="mb-8">
        <router-link to="/web/framework/python/flask" class="inline-flex items-center text-blue-600 hover:text-blue-700 transition-colors duration-200">
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
          </svg>
          返回 Flask 框架
        </router-link>
      </div>

      <!-- 页面头部 -->
      <div class="text-center mb-12">
        <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full mb-6 shadow-lg">
          <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
            <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
          </svg>
        </div>
        <h1 class="text-4xl font-extrabold text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-indigo-600 mb-4">
          Flask 配置文件详解
        </h1>
        <p class="text-xl text-gray-700 max-w-4xl mx-auto leading-relaxed">
          ⚙️ 深入理解Flask配置文件的结构、参数含义和最佳实践
        </p>
      </div>

      <!-- 配置文件概览 -->
      <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-white/50 mb-8">
        <h2 class="text-3xl font-bold text-blue-800 mb-6 flex items-center">
          <svg class="w-8 h-8 mr-3 text-blue-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
          </svg>
          配置文件概览
        </h2>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-bold text-blue-800 mb-4">配置文件结构</h3>
            <div class="bg-gray-50 p-4 rounded-lg border">
              <pre class="text-sm text-gray-700 overflow-x-auto"><code>myflaskapp/
├── config.py               # 主配置文件
├── .env                    # 环境变量文件
├── .flaskenv              # Flask环境变量
├── instance/               # 实例配置目录
│   ├── config.py          # 实例配置文件
│   └── config.cfg         # 配置文件格式
├── app/
│   ├── __init__.py        # 应用工厂
│   └── config/            # 配置模块
│       ├── __init__.py
│       ├── development.py # 开发环境配置
│       ├── production.py  # 生产环境配置
│       ├── testing.py     # 测试环境配置
│       └── default.py     # 默认配置
├── requirements/           # 分环境依赖
│   ├── base.txt           # 基础依赖
│   ├── dev.txt            # 开发依赖
│   └── prod.txt           # 生产依赖
└── docker/                 # Docker配置
    ├── Dockerfile
    └── docker-compose.yml</code></pre>
            </div>
          </div>

          <div>
            <h3 class="text-xl font-bold text-blue-800 mb-4">配置加载机制</h3>
            <div class="space-y-4">
              <div class="bg-blue-50 p-3 rounded border border-blue-200">
                <h4 class="font-bold text-blue-700 mb-2">配置加载顺序</h4>
                <div class="space-y-2 text-sm">
                  <div class="bg-white p-2 rounded border">
                    <strong>1. 默认配置：</strong>Flask内置默认配置
                  </div>
                  <div class="bg-white p-2 rounded border">
                    <strong>2. 配置对象：</strong>config.py中的配置类
                  </div>
                  <div class="bg-white p-2 rounded border">
                    <strong>3. 实例配置：</strong>instance/config.py
                  </div>
                  <div class="bg-white p-2 rounded border">
                    <strong>4. 环境变量：</strong>.env和.flaskenv文件
                  </div>
                  <div class="bg-white p-2 rounded border">
                    <strong>5. 运行时配置：</strong>动态设置的配置
                  </div>
                </div>
              </div>

              <div class="bg-green-50 p-3 rounded border border-green-200">
                <h4 class="font-bold text-green-700 mb-2">配置方式</h4>
                <pre class="bg-white p-2 rounded mt-2 text-xs"><code># 1. 从配置对象加载
app.config.from_object('config.DevelopmentConfig')

# 2. 从配置文件加载
app.config.from_pyfile('config.py')

# 3. 从环境变量加载
app.config.from_envvar('FLASK_CONFIG')

# 4. 从字典加载
app.config.from_mapping(
    SECRET_KEY='dev-secret-key',
    DATABASE_URL='sqlite:///app.db'
)

# 5. 从JSON文件加载
app.config.from_json('config.json')

# 6. 直接设置
app.config['SECRET_KEY'] = 'your-secret-key'</code></pre>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 主配置文件 config.py -->
      <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-white/50 mb-8">
        <h2 class="text-3xl font-bold text-indigo-800 mb-6 flex items-center">
          <svg class="w-8 h-8 mr-3 text-indigo-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"/>
          </svg>
          主配置文件 - config.py
        </h2>

        <div class="space-y-6">
          <div class="bg-blue-50 p-4 rounded-lg border border-blue-200">
            <h4 class="font-bold text-blue-700 mb-3">基础配置类</h4>
            <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code># config.py
import os
from datetime import timedelta

basedir = os.path.abspath(os.path.dirname(__file__))

class Config:
    """基础配置类"""
    
    # 基础配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'hard-to-guess-string'
    
    # 数据库配置
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or \
        'sqlite:///' + os.path.join(basedir, 'app.db')
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_RECORD_QUERIES = True
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_timeout': 20,
        'pool_recycle': -1,
        'pool_pre_ping': True
    }
    
    # 邮件配置
    MAIL_SERVER = os.environ.get('MAIL_SERVER') or 'localhost'
    MAIL_PORT = int(os.environ.get('MAIL_PORT') or 587)
    MAIL_USE_TLS = os.environ.get('MAIL_USE_TLS', 'true').lower() in ['true', 'on', '1']
    MAIL_USERNAME = os.environ.get('MAIL_USERNAME')
    MAIL_PASSWORD = os.environ.get('MAIL_PASSWORD')
    MAIL_SUBJECT_PREFIX = '[Flask App] '
    MAIL_SENDER = 'Flask App Admin &lt;<EMAIL>&gt;'
    
    # 管理员配置
    FLASK_ADMIN = os.environ.get('FLASK_ADMIN') or '<EMAIL>'
    
    # 分页配置
    POSTS_PER_PAGE = 20
    FOLLOWERS_PER_PAGE = 50
    COMMENTS_PER_PAGE = 30
    
    # 上传文件配置
    UPLOAD_FOLDER = os.path.join(basedir, 'uploads')
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB
    ALLOWED_EXTENSIONS = {'txt', 'pdf', 'png', 'jpg', 'jpeg', 'gif'}
    
    # 缓存配置
    CACHE_TYPE = 'simple'
    CACHE_DEFAULT_TIMEOUT = 300
    
    # 会话配置
    PERMANENT_SESSION_LIFETIME = timedelta(hours=24)
    SESSION_COOKIE_SECURE = False
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'
    
    # 国际化配置
    LANGUAGES = ['en', 'zh', 'es', 'fr']
    BABEL_DEFAULT_LOCALE = 'en'
    BABEL_DEFAULT_TIMEZONE = 'UTC'
    
    # 日志配置
    LOG_TO_STDOUT = os.environ.get('LOG_TO_STDOUT')
    
    @staticmethod
    def init_app(app):
        """初始化应用配置"""
        pass</code></pre>
          </div>

          <div class="bg-green-50 p-4 rounded-lg border border-green-200">
            <h4 class="font-bold text-green-700 mb-3">开发环境配置</h4>
            <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code>class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True
    
    # 数据库配置
    SQLALCHEMY_DATABASE_URI = os.environ.get('DEV_DATABASE_URL') or \
        'sqlite:///' + os.path.join(basedir, 'data-dev.db')
    
    # 邮件配置（开发环境使用控制台输出）
    MAIL_SERVER = 'localhost'
    MAIL_PORT = 2525
    MAIL_USE_TLS = False
    MAIL_USE_SSL = False
    
    # 缓存配置
    CACHE_TYPE = 'simple'
    
    # 调试工具栏
    DEBUG_TB_ENABLED = True
    DEBUG_TB_INTERCEPT_REDIRECTS = False
    
    # 会话配置
    SESSION_COOKIE_SECURE = False
    
    # 日志级别
    LOG_LEVEL = 'DEBUG'
    
    # 开发服务器配置
    SERVER_NAME = None
    APPLICATION_ROOT = '/'
    PREFERRED_URL_SCHEME = 'http'</code></pre>
          </div>
        </div>
      </div>

      <!-- 生产环境和测试环境配置 -->
      <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-white/50 mb-8">
        <h2 class="text-3xl font-bold text-purple-800 mb-6 flex items-center">
          <svg class="w-8 h-8 mr-3 text-purple-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"/>
          </svg>
          生产环境和测试环境配置
        </h2>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-bold text-purple-800 mb-4">生产环境配置</h3>
            <div class="space-y-4">
              <div class="bg-red-50 p-4 rounded-lg border border-red-200">
                <h4 class="font-bold text-red-700 mb-2">ProductionConfig</h4>
                <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code>class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False
    TESTING = False
    
    # 数据库配置
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or \
        'postgresql://user:pass@localhost/flaskapp'
    SQLALCHEMY_RECORD_QUERIES = False
    
    # 安全配置
    SECRET_KEY = os.environ.get('SECRET_KEY')
    if not SECRET_KEY:
        raise ValueError("No SECRET_KEY set for production")
    
    # HTTPS配置
    PREFERRED_URL_SCHEME = 'https'
    SESSION_COOKIE_SECURE = True
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'
    
    # 会话配置
    PERMANENT_SESSION_LIFETIME = timedelta(hours=1)
    
    # 缓存配置
    CACHE_TYPE = 'redis'
    CACHE_REDIS_URL = os.environ.get('REDIS_URL') or 'redis://localhost:6379/0'
    
    # 邮件配置
    MAIL_SERVER = os.environ.get('MAIL_SERVER')
    MAIL_PORT = int(os.environ.get('MAIL_PORT') or 587)
    MAIL_USE_TLS = True
    MAIL_USERNAME = os.environ.get('MAIL_USERNAME')
    MAIL_PASSWORD = os.environ.get('MAIL_PASSWORD')
    
    # 日志配置
    LOG_LEVEL = 'INFO'
    LOG_TO_STDOUT = os.environ.get('LOG_TO_STDOUT')
    
    # 文件上传配置
    MAX_CONTENT_LENGTH = 5 * 1024 * 1024  # 5MB
    
    # 性能配置
    SEND_FILE_MAX_AGE_DEFAULT = timedelta(hours=1)
    
    @classmethod
    def init_app(cls, app):
        Config.init_app(app)
        
        # 邮件错误日志
        import logging
        from logging.handlers import SMTPHandler
        if app.config['MAIL_SERVER']:
            auth = None
            if app.config['MAIL_USERNAME'] or app.config['MAIL_PASSWORD']:
                auth = (app.config['MAIL_USERNAME'], app.config['MAIL_PASSWORD'])
            secure = None
            if app.config['MAIL_USE_TLS']:
                secure = ()
            mail_handler = SMTPHandler(
                mailhost=(app.config['MAIL_SERVER'], app.config['MAIL_PORT']),
                fromaddr=app.config['MAIL_SENDER'],
                toaddrs=[app.config['FLASK_ADMIN']],
                subject=app.config['MAIL_SUBJECT_PREFIX'] + ' Application Error',
                credentials=auth, secure=secure)
            mail_handler.setLevel(logging.ERROR)
            app.logger.addHandler(mail_handler)</code></pre>
              </div>
            </div>
          </div>

          <div>
            <h3 class="text-xl font-bold text-purple-800 mb-4">测试环境配置</h3>
            <div class="space-y-4">
              <div class="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
                <h4 class="font-bold text-yellow-700 mb-2">TestingConfig</h4>
                <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code>class TestingConfig(Config):
    """测试环境配置"""
    TESTING = True
    DEBUG = False
    
    # 数据库配置（内存数据库）
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # 禁用CSRF保护
    WTF_CSRF_ENABLED = False
    
    # 邮件配置（测试环境不发送邮件）
    MAIL_SUPPRESS_SEND = True
    
    # 缓存配置
    CACHE_TYPE = 'null'
    
    # 会话配置
    SESSION_COOKIE_SECURE = False
    
    # 日志配置
    LOG_LEVEL = 'WARNING'
    
    # 测试专用配置
    SERVER_NAME = 'localhost.localdomain'
    
    @classmethod
    def init_app(cls, app):
        Config.init_app(app)
        
        # 禁用日志记录
        import logging
        logging.disable(logging.CRITICAL)

# 配置字典
config = {
    'development': DevelopmentConfig,
    'testing': TestingConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}</code></pre>
              </div>

              <div class="bg-blue-50 p-4 rounded-lg border border-blue-200">
                <h4 class="font-bold text-blue-700 mb-2">配置选择</h4>
                <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code># app/__init__.py
from config import config

def create_app(config_name=None):
    if config_name is None:
        config_name = os.environ.get('FLASK_CONFIG', 'default')
    
    app = Flask(__name__)
    app.config.from_object(config[config_name])
    config[config_name].init_app(app)
    
    return app

# 使用方式
# export FLASK_CONFIG=production
# flask run

# 或者在代码中指定
app = create_app('development')</code></pre>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 环境变量配置 -->
      <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-white/50 mb-8">
        <h2 class="text-3xl font-bold text-pink-800 mb-6 flex items-center">
          <svg class="w-8 h-8 mr-3 text-pink-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
          </svg>
          环境变量配置
        </h2>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-bold text-pink-800 mb-4">.env 环境变量文件</h3>
            <div class="space-y-4">
              <div class="bg-pink-50 p-4 rounded-lg border border-pink-200">
                <h4 class="font-bold text-pink-700 mb-2">.env 文件配置</h4>
                <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code># .env - 环境变量文件（不要提交到版本控制）
# Flask基础配置
SECRET_KEY=your-super-secret-key-here
FLASK_CONFIG=development

# 数据库配置
DATABASE_URL=postgresql://username:password@localhost/flaskapp
DEV_DATABASE_URL=sqlite:///data-dev.db
TEST_DATABASE_URL=sqlite:///:memory:

# Redis配置
REDIS_URL=redis://localhost:6379/0

# 邮件配置
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=1
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password

# 管理员配置
FLASK_ADMIN=<EMAIL>

# 第三方服务配置
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret

# AWS配置
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_S3_BUCKET=your-s3-bucket-name
AWS_REGION=us-east-1

# 日志配置
LOG_TO_STDOUT=1
LOG_LEVEL=DEBUG

# 安全配置
SECURITY_PASSWORD_SALT=your-password-salt
JWT_SECRET_KEY=your-jwt-secret-key

# 支付配置
STRIPE_PUBLISHABLE_KEY=pk_test_...
STRIPE_SECRET_KEY=sk_test_...

# 监控配置
SENTRY_DSN=https://your-sentry-dsn</code></pre>
              </div>
            </div>
          </div>

          <div>
            <h3 class="text-xl font-bold text-pink-800 mb-4">.flaskenv Flask专用环境变量</h3>
            <div class="space-y-4">
              <div class="bg-blue-50 p-4 rounded-lg border border-blue-200">
                <h4 class="font-bold text-blue-700 mb-2">.flaskenv 文件配置</h4>
                <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code># .flaskenv - Flask专用环境变量（可以提交到版本控制）
# Flask应用配置
FLASK_APP=app.py
FLASK_ENV=development
FLASK_DEBUG=1

# 开发服务器配置
FLASK_RUN_HOST=0.0.0.0
FLASK_RUN_PORT=5000
FLASK_RUN_EXTRA_FILES=config.py

# 自动重载配置
FLASK_RUN_RELOAD=1
FLASK_RUN_DEBUGGER=1

# SSL配置（开发环境）
FLASK_RUN_CERT=adhoc
FLASK_RUN_KEY=path/to/key.pem</code></pre>
              </div>

              <div class="bg-green-50 p-4 rounded-lg border border-green-200">
                <h4 class="font-bold text-green-700 mb-2">环境变量加载</h4>
                <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code># 使用python-dotenv加载环境变量
from dotenv import load_dotenv
import os

# 加载.env文件
load_dotenv()

# 或者指定文件路径
load_dotenv('.env.local')

# 在配置类中使用
class Config:
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'fallback-secret-key'

    @staticmethod
    def get_env_bool(name, default=False):
        """获取布尔类型环境变量"""
        value = os.environ.get(name, '').lower()
        return value in ('1', 'true', 'yes', 'on')

    @staticmethod
    def get_env_int(name, default=0):
        """获取整数类型环境变量"""
        try:
            return int(os.environ.get(name, default))
        except ValueError:
            return default

# 使用示例
DEBUG = Config.get_env_bool('FLASK_DEBUG')
PORT = Config.get_env_int('FLASK_RUN_PORT', 5000)</code></pre>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 实例配置 -->
      <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-white/50 mb-8">
        <h2 class="text-3xl font-bold text-cyan-800 mb-6 flex items-center">
          <svg class="w-8 h-8 mr-3 text-cyan-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
          </svg>
          实例配置 (Instance Configuration)
        </h2>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-bold text-cyan-800 mb-4">实例文件夹配置</h3>
            <div class="space-y-4">
              <div class="bg-cyan-50 p-4 rounded-lg border border-cyan-200">
                <h4 class="font-bold text-cyan-700 mb-2">instance/config.py</h4>
                <p class="text-cyan-600 text-sm mb-3">
                  实例配置文件用于存储敏感信息和特定部署的配置，
                  不应该提交到版本控制系统中。
                </p>
                <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code># instance/config.py
# 敏感配置信息（不要提交到版本控制）

# 密钥配置
SECRET_KEY = 'your-production-secret-key'
JWT_SECRET_KEY = 'your-jwt-secret-key'

# 数据库配置
SQLALCHEMY_DATABASE_URI = 'postgresql://user:password@localhost/production_db'

# 邮件配置
MAIL_SERVER = 'smtp.yourcompany.com'
MAIL_PORT = 587
MAIL_USE_TLS = True
MAIL_USERNAME = '<EMAIL>'
MAIL_PASSWORD = 'your-mail-password'

# 第三方API密钥
GOOGLE_OAUTH_CLIENT_ID = 'your-google-client-id'
GOOGLE_OAUTH_CLIENT_SECRET = 'your-google-client-secret'

STRIPE_PUBLISHABLE_KEY = 'pk_live_...'
STRIPE_SECRET_KEY = 'sk_live_...'

# AWS配置
AWS_ACCESS_KEY_ID = 'your-aws-access-key'
AWS_SECRET_ACCESS_KEY = 'your-aws-secret-key'
AWS_S3_BUCKET = 'your-production-bucket'

# 监控和日志
SENTRY_DSN = 'https://your-sentry-dsn'
LOG_LEVEL = 'INFO'

# 缓存配置
CACHE_REDIS_URL = 'redis://your-redis-server:6379/0'

# 自定义配置
COMPANY_NAME = 'Your Company'
SUPPORT_EMAIL = '<EMAIL>'
MAX_UPLOAD_SIZE = 10 * 1024 * 1024  # 10MB</code></pre>
              </div>
            </div>
          </div>

          <div>
            <h3 class="text-xl font-bold text-cyan-800 mb-4">实例配置加载</h3>
            <div class="space-y-4">
              <div class="bg-blue-50 p-4 rounded-lg border border-blue-200">
                <h4 class="font-bold text-blue-700 mb-2">加载实例配置</h4>
                <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code># app/__init__.py
def create_app(config_name=None):
    app = Flask(__name__, instance_relative_config=True)

    # 加载默认配置
    app.config.from_object(config[config_name])

    # 加载实例配置（如果存在）
    try:
        app.config.from_pyfile('config.py')
    except FileNotFoundError:
        # 实例配置文件不存在时的处理
        app.logger.warning('Instance config file not found')

    # 确保实例文件夹存在
    try:
        os.makedirs(app.instance_path)
    except OSError:
        pass

    return app

# 实例配置的优先级
# 1. 默认配置 (config.py中的配置类)
# 2. 实例配置 (instance/config.py)
# 3. 环境变量覆盖</code></pre>
              </div>

              <div class="bg-green-50 p-4 rounded-lg border border-green-200">
                <h4 class="font-bold text-green-700 mb-2">配置文件格式</h4>
                <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code># instance/config.cfg (INI格式)
[DEFAULT]
SECRET_KEY = your-secret-key
DEBUG = False

[database]
SQLALCHEMY_DATABASE_URI = postgresql://user:pass@localhost/db

[mail]
MAIL_SERVER = smtp.gmail.com
MAIL_PORT = 587
MAIL_USE_TLS = True

# 加载INI格式配置
app.config.from_file('config.cfg', load=toml.load)

# instance/config.json (JSON格式)
{
  "SECRET_KEY": "your-secret-key",
  "DEBUG": false,
  "SQLALCHEMY_DATABASE_URI": "postgresql://user:pass@localhost/db"
}

# 加载JSON格式配置
app.config.from_file('config.json', load=json.load)</code></pre>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Flask核心配置参数 -->
      <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-white/50 mb-8">
        <h2 class="text-3xl font-bold text-green-800 mb-6 flex items-center">
          <svg class="w-8 h-8 mr-3 text-green-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z"/>
          </svg>
          Flask 核心配置参数详解
        </h2>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-bold text-green-800 mb-4">基础配置参数</h3>
            <div class="space-y-4">
              <div class="bg-green-50 p-4 rounded-lg border border-green-200">
                <h4 class="font-bold text-green-700 mb-2">核心参数</h4>
                <div class="space-y-3 text-sm">
                  <div class="bg-white p-3 rounded border">
                    <strong>SECRET_KEY：</strong>用于会话签名和CSRF保护的密钥
                    <br><code class="text-xs bg-gray-100 px-1 rounded">app.config['SECRET_KEY'] = 'your-secret-key'</code>
                  </div>
                  <div class="bg-white p-3 rounded border">
                    <strong>DEBUG：</strong>启用调试模式，显示详细错误信息
                    <br><code class="text-xs bg-gray-100 px-1 rounded">app.config['DEBUG'] = True</code>
                  </div>
                  <div class="bg-white p-3 rounded border">
                    <strong>TESTING：</strong>启用测试模式，禁用错误捕获
                    <br><code class="text-xs bg-gray-100 px-1 rounded">app.config['TESTING'] = True</code>
                  </div>
                  <div class="bg-white p-3 rounded border">
                    <strong>SERVER_NAME：</strong>服务器名称和端口
                    <br><code class="text-xs bg-gray-100 px-1 rounded">app.config['SERVER_NAME'] = 'localhost:5000'</code>
                  </div>
                  <div class="bg-white p-3 rounded border">
                    <strong>APPLICATION_ROOT：</strong>应用程序根路径
                    <br><code class="text-xs bg-gray-100 px-1 rounded">app.config['APPLICATION_ROOT'] = '/myapp'</code>
                  </div>
                  <div class="bg-white p-3 rounded border">
                    <strong>PREFERRED_URL_SCHEME：</strong>首选URL方案
                    <br><code class="text-xs bg-gray-100 px-1 rounded">app.config['PREFERRED_URL_SCHEME'] = 'https'</code>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div>
            <h3 class="text-xl font-bold text-green-800 mb-4">会话和安全配置</h3>
            <div class="space-y-4">
              <div class="bg-blue-50 p-4 rounded-lg border border-blue-200">
                <h4 class="font-bold text-blue-700 mb-2">会话配置</h4>
                <div class="space-y-3 text-sm">
                  <div class="bg-white p-3 rounded border">
                    <strong>PERMANENT_SESSION_LIFETIME：</strong>永久会话生存时间
                    <br><code class="text-xs bg-gray-100 px-1 rounded">timedelta(hours=24)</code>
                  </div>
                  <div class="bg-white p-3 rounded border">
                    <strong>SESSION_COOKIE_NAME：</strong>会话Cookie名称
                    <br><code class="text-xs bg-gray-100 px-1 rounded">'session'</code>
                  </div>
                  <div class="bg-white p-3 rounded border">
                    <strong>SESSION_COOKIE_DOMAIN：</strong>会话Cookie域名
                    <br><code class="text-xs bg-gray-100 px-1 rounded">'.example.com'</code>
                  </div>
                  <div class="bg-white p-3 rounded border">
                    <strong>SESSION_COOKIE_PATH：</strong>会话Cookie路径
                    <br><code class="text-xs bg-gray-100 px-1 rounded">'/'</code>
                  </div>
                  <div class="bg-white p-3 rounded border">
                    <strong>SESSION_COOKIE_HTTPONLY：</strong>HttpOnly标志
                    <br><code class="text-xs bg-gray-100 px-1 rounded">True</code>
                  </div>
                  <div class="bg-white p-3 rounded border">
                    <strong>SESSION_COOKIE_SECURE：</strong>Secure标志
                    <br><code class="text-xs bg-gray-100 px-1 rounded">True</code>
                  </div>
                  <div class="bg-white p-3 rounded border">
                    <strong>SESSION_COOKIE_SAMESITE：</strong>SameSite属性
                    <br><code class="text-xs bg-gray-100 px-1 rounded">'Lax'</code>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FlaskConfig'
}
</script>

<style scoped>
@keyframes blob {
  0% { transform: translate(0px, 0px) scale(1); }
  33% { transform: translate(30px, -50px) scale(1.1); }
  66% { transform: translate(-20px, 20px) scale(0.9); }
  100% { transform: translate(0px, 0px) scale(1); }
}

.animate-blob {
  animation: blob 7s infinite;
}

.animation-delay-2000 {
  animation-delay: 2s;
}
</style>
