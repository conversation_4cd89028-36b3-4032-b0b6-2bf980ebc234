<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex flex-col relative">
    <!-- 背景装饰 -->
    <div class="absolute inset-0 overflow-hidden pointer-events-none">
      <div class="absolute -top-40 -right-40 w-80 h-80 bg-blue-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob"></div>
      <div class="absolute -bottom-40 -left-40 w-80 h-80 bg-indigo-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000"></div>
    </div>

    <div class="max-w-7xl mx-auto py-12 px-4 sm:px-6 md:px-8 w-full relative z-10">
      <!-- 返回按钮 -->
      <div class="mb-8">
        <router-link to="/web/framework/python/flask" class="inline-flex items-center text-blue-600 hover:text-blue-700 transition-colors duration-200">
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
          </svg>
          返回 Flask 框架
        </router-link>
      </div>

      <!-- 页面头部 -->
      <div class="text-center mb-12">
        <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full mb-6 shadow-lg">
          <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C20.832 18.477 19.246 18 17.5 18c-1.746 0-3.332.477-4.5 1.253"/>
          </svg>
        </div>
        <h1 class="text-4xl font-extrabold text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-indigo-600 mb-4">
          Flask 基础知识
        </h1>
        <p class="text-xl text-gray-700 max-w-4xl mx-auto leading-relaxed">
          🚀 全面掌握Flask微框架的核心概念、架构设计和基础用法
        </p>
      </div>

      <!-- Flask核心架构 -->
      <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-white/50 mb-8">
        <h2 class="text-3xl font-bold text-blue-800 mb-6 flex items-center">
          <svg class="w-8 h-8 mr-3 text-blue-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"/>
          </svg>
          Flask 核心架构
        </h2>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-bold text-blue-800 mb-4">微框架设计</h3>
            <div class="space-y-4">
              <div class="bg-blue-50 p-4 rounded-lg border border-blue-200">
                <h4 class="font-bold text-blue-700 mb-2">Flask核心特性</h4>
                <p class="text-blue-600 text-sm mb-3">
                  Flask是一个轻量级的WSGI Web应用框架，采用微框架设计理念，
                  核心功能简洁，通过扩展系统提供丰富功能。
                </p>
                <div class="space-y-2 text-sm">
                  <div class="bg-white p-2 rounded border">
                    <strong>WSGI兼容：</strong>基于Werkzeug WSGI工具库
                  </div>
                  <div class="bg-white p-2 rounded border">
                    <strong>Jinja2模板：</strong>强大的模板引擎
                  </div>
                  <div class="bg-white p-2 rounded border">
                    <strong>蓝图系统：</strong>模块化应用组织
                  </div>
                  <div class="bg-white p-2 rounded border">
                    <strong>扩展生态：</strong>丰富的第三方扩展
                  </div>
                </div>
              </div>

              <div class="bg-indigo-50 p-4 rounded-lg border border-indigo-200">
                <h4 class="font-bold text-indigo-700 mb-2">核心组件</h4>
                <div class="space-y-2 text-sm">
                  <div class="bg-white p-2 rounded border">
                    <strong>Flask应用：</strong>WSGI应用实例
                  </div>
                  <div class="bg-white p-2 rounded border">
                    <strong>路由系统：</strong>URL到视图函数的映射
                  </div>
                  <div class="bg-white p-2 rounded border">
                    <strong>请求上下文：</strong>请求处理上下文管理
                  </div>
                  <div class="bg-white p-2 rounded border">
                    <strong>应用上下文：</strong>应用级别上下文管理
                  </div>
                  <div class="bg-white p-2 rounded border">
                    <strong>会话管理：</strong>基于Cookie的会话系统
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div>
            <h3 class="text-xl font-bold text-blue-800 mb-4">项目结构</h3>
            <div class="bg-gray-50 p-4 rounded-lg border">
              <pre class="text-sm text-gray-700 overflow-x-auto"><code>myflaskapp/
├── app.py                  # 应用入口文件
├── config.py               # 配置文件
├── requirements.txt        # 依赖包列表
├── .env                    # 环境变量
├── .flaskenv              # Flask环境变量
├── instance/               # 实例配置目录
│   └── config.py          # 实例配置文件
├── app/                    # 应用包
│   ├── __init__.py        # 应用工厂
│   ├── models.py          # 数据模型
│   ├── views.py           # 视图函数
│   ├── forms.py           # 表单定义
│   ├── extensions.py      # 扩展初始化
│   ├── blueprints/        # 蓝图目录
│   │   ├── __init__.py
│   │   ├── auth/          # 认证蓝图
│   │   │   ├── __init__.py
│   │   │   ├── views.py
│   │   │   └── forms.py
│   │   └── main/          # 主要蓝图
│   │       ├── __init__.py
│   │       └── views.py
│   ├── templates/         # 模板文件
│   │   ├── base.html
│   │   ├── index.html
│   │   └── auth/
│   │       ├── login.html
│   │       └── register.html
│   └── static/            # 静态文件
│       ├── css/
│       ├── js/
│       └── images/
├── migrations/             # 数据库迁移
├── tests/                  # 测试文件
└── logs/                   # 日志文件</code></pre>
            </div>
          </div>
        </div>
      </div>

      <!-- Flask应用创建 -->
      <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-white/50 mb-8">
        <h2 class="text-3xl font-bold text-indigo-800 mb-6 flex items-center">
          <svg class="w-8 h-8 mr-3 text-indigo-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
            <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
          </svg>
          Flask 应用创建
        </h2>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-bold text-indigo-800 mb-4">基础应用</h3>
            <div class="space-y-4">
              <div class="bg-blue-50 p-3 rounded border border-blue-200">
                <strong class="text-blue-700">简单Flask应用：</strong>
                <pre class="bg-white p-2 rounded mt-2 text-xs"><code># app.py
from flask import Flask, render_template, request, redirect, url_for, flash, session
from flask import jsonify, abort, make_response

# 创建Flask应用实例
app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'

# 基础路由
@app.route('/')
def index():
    return 'Hello, Flask!'

@app.route('/hello/&lt;name&gt;')
def hello(name):
    return f'Hello, &#123;name&#125;!'

# HTTP方法处理
@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        
        # 验证逻辑
        if username == 'admin' and password == 'secret':
            session['user'] = username
            flash('登录成功!', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('用户名或密码错误!', 'error')
    
    return render_template('login.html')

@app.route('/dashboard')
def dashboard():
    if 'user' not in session:
        return redirect(url_for('login'))
    return render_template('dashboard.html', user=session['user'])

@app.route('/logout')
def logout():
    session.pop('user', None)
    flash('已退出登录!', 'info')
    return redirect(url_for('index'))

# API路由
@app.route('/api/users')
def api_users():
    users = [
        &#123;'id': 1, 'name': 'Alice'&#125;,
        &#123;'id': 2, 'name': 'Bob'&#125;
    ]
    return jsonify(users)

# 错误处理
@app.errorhandler(404)
def not_found(error):
    return render_template('404.html'), 404

@app.errorhandler(500)
def internal_error(error):
    return render_template('500.html'), 500

if __name__ == '__main__':
    app.run(debug=True)</code></pre>
              </div>
            </div>
          </div>

          <div>
            <h3 class="text-xl font-bold text-indigo-800 mb-4">应用工厂模式</h3>
            <div class="space-y-4">
              <div class="bg-green-50 p-3 rounded border border-green-200">
                <strong class="text-green-700">应用工厂：</strong>
                <pre class="bg-white p-2 rounded mt-2 text-xs"><code># app/__init__.py
from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
from flask_login import LoginManager
from config import Config

# 扩展实例
db = SQLAlchemy()
migrate = Migrate()
login = LoginManager()

def create_app(config_class=Config):
    """应用工厂函数"""
    app = Flask(__name__)
    app.config.from_object(config_class)
    
    # 初始化扩展
    db.init_app(app)
    migrate.init_app(app, db)
    login.init_app(app)
    login.login_view = 'auth.login'
    login.login_message = '请先登录'
    
    # 注册蓝图
    from app.main import bp as main_bp
    app.register_blueprint(main_bp)
    
    from app.auth import bp as auth_bp
    app.register_blueprint(auth_bp, url_prefix='/auth')
    
    from app.api import bp as api_bp
    app.register_blueprint(api_bp, url_prefix='/api')
    
    # 错误处理
    from app.errors import bp as errors_bp
    app.register_blueprint(errors_bp)
    
    return app

# app/models.py
from app import db
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash

class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(128))
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)
    
    def __repr__(self):
        return f'&lt;User &#123;self.username&#125;&gt;'</code></pre>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Flask路由系统 -->
      <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-white/50 mb-8">
        <h2 class="text-3xl font-bold text-purple-800 mb-6 flex items-center">
          <svg class="w-8 h-8 mr-3 text-purple-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"/>
          </svg>
          Flask 路由系统
        </h2>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-bold text-purple-800 mb-4">路由定义</h3>
            <div class="space-y-4">
              <div class="bg-purple-50 p-3 rounded border border-purple-200">
                <strong class="text-purple-700">基础路由：</strong>
                <pre class="bg-white p-2 rounded mt-2 text-xs"><code># 基础路由定义
@app.route('/')
def index():
    return 'Hello World!'

# 带参数的路由
@app.route('/user/&lt;username&gt;')
def show_user_profile(username):
    return f'User: &#123;username&#125;'

@app.route('/post/&lt;int:post_id&gt;')
def show_post(post_id):
    return f'Post ID: &#123;post_id&#125;'

# 多种类型转换器
@app.route('/path/&lt;path:subpath&gt;')
def show_subpath(subpath):
    return f'Subpath: &#123;subpath&#125;'

@app.route('/uuid/&lt;uuid:user_id&gt;')
def show_uuid(user_id):
    return f'UUID: &#123;user_id&#125;'

# HTTP方法限制
@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        return do_the_login()
    else:
        return show_the_login_form()

@app.route('/logout', methods=['POST'])
def logout():
    return do_logout()

# 多个URL规则
@app.route('/users/')
@app.route('/users/&lt;int:page&gt;')
def users(page=1):
    return f'Users page &#123;page&#125;'

# URL构建
@app.route('/admin')
def admin():
    return redirect(url_for('login'))

@app.route('/hello/')
@app.route('/hello/&lt;name&gt;')
def hello(name=None):
    if name is None:
        return redirect(url_for('hello', name='World'))
    return f'Hello &#123;name&#125;!'</code></pre>
              </div>
            </div>
          </div>

          <div>
            <h3 class="text-xl font-bold text-purple-800 mb-4">高级路由</h3>
            <div class="space-y-4">
              <div class="bg-blue-50 p-3 rounded border border-blue-200">
                <strong class="text-blue-700">路由装饰器和中间件：</strong>
                <pre class="bg-white p-2 rounded mt-2 text-xs"><code># 自定义装饰器
from functools import wraps

def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

def admin_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_admin:
            abort(403)
        return f(*args, **kwargs)
    return decorated_function

# 使用装饰器
@app.route('/dashboard')
@login_required
def dashboard():
    return render_template('dashboard.html')

@app.route('/admin/users')
@login_required
@admin_required
def admin_users():
    return render_template('admin/users.html')

# 请求钩子
@app.before_request
def before_request():
    g.user = None
    if 'user_id' in session:
        g.user = User.query.get(session['user_id'])

@app.after_request
def after_request(response):
    response.headers['X-Frame-Options'] = 'SAMEORIGIN'
    return response

@app.teardown_appcontext
def close_db(error):
    if hasattr(g, 'db'):
        g.db.close()

# 错误处理
@app.errorhandler(404)
def not_found(error):
    return render_template('error.html', error=404), 404

@app.errorhandler(500)
def internal_error(error):
    db.session.rollback()
    return render_template('error.html', error=500), 500</code></pre>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Flask模板系统 -->
      <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-white/50 mb-8">
        <h2 class="text-3xl font-bold text-pink-800 mb-6 flex items-center">
          <svg class="w-8 h-8 mr-3 text-pink-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01"/>
          </svg>
          Flask 模板系统 (Jinja2)
        </h2>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-bold text-pink-800 mb-4">模板基础</h3>
            <div class="space-y-4">
              <div class="bg-pink-50 p-3 rounded border border-pink-200">
                <strong class="text-pink-700">基础模板：</strong>
                <pre class="bg-white p-2 rounded mt-2 text-xs"><code>&lt;!-- templates/base.html --&gt;
&lt;!DOCTYPE html&gt;
&lt;html lang="zh-cn"&gt;
&lt;head&gt;
    &lt;meta charset="UTF-8"&gt;
    &lt;meta name="viewport" content="width=device-width, initial-scale=1.0"&gt;
    &lt;title&gt;&#123;% block title %&#125;Flask App&#123;% endblock %&#125;&lt;/title&gt;
    &lt;link rel="stylesheet" href="&#123;&#123; url_for('static', filename='css/style.css') &#125;&#125;"&gt;
    &#123;% block head %&#125;&#123;% endblock %&#125;
&lt;/head&gt;
&lt;body&gt;
    &lt;nav class="navbar"&gt;
        &lt;a href="&#123;&#123; url_for('index') &#125;&#125;"&gt;首页&lt;/a&gt;
        &#123;% if current_user.is_authenticated %&#125;
            &lt;a href="&#123;&#123; url_for('dashboard') &#125;&#125;"&gt;仪表板&lt;/a&gt;
            &lt;a href="&#123;&#123; url_for('logout') &#125;&#125;"&gt;退出&lt;/a&gt;
        &#123;% else %&#125;
            &lt;a href="&#123;&#123; url_for('login') &#125;&#125;"&gt;登录&lt;/a&gt;
            &lt;a href="&#123;&#123; url_for('register') &#125;&#125;"&gt;注册&lt;/a&gt;
        &#123;% endif %&#125;
    &lt;/nav&gt;

    &lt;main&gt;
        &#123;% with messages = get_flashed_messages(with_categories=true) %&#125;
            &#123;% if messages %&#125;
                &#123;% for category, message in messages %&#125;
                    &lt;div class="alert alert-&#123;&#123; category &#125;&#125;"&gt;&#123;&#123; message &#125;&#125;&lt;/div&gt;
                &#123;% endfor %&#125;
            &#123;% endif %&#125;
        &#123;% endwith %&#125;

        &#123;% block content %&#125;&#123;% endblock %&#125;
    &lt;/main&gt;

    &lt;footer&gt;
        &lt;p&gt;&amp;copy; &#123;&#123; moment().format('YYYY') &#125;&#125; Flask App&lt;/p&gt;
    &lt;/footer&gt;

    &lt;script src="&#123;&#123; url_for('static', filename='js/app.js') &#125;&#125;"&gt;&lt;/script&gt;
    &#123;% block scripts %&#125;&#123;% endblock %&#125;
&lt;/body&gt;
&lt;/html&gt;</code></pre>
              </div>

              <div class="bg-blue-50 p-3 rounded border border-blue-200">
                <strong class="text-blue-700">子模板：</strong>
                <pre class="bg-white p-2 rounded mt-2 text-xs"><code>&lt;!-- templates/index.html --&gt;
&#123;% extends "base.html" %&#125;

&#123;% block title %&#125;首页 - &#123;&#123; super() &#125;&#125;&#123;% endblock %&#125;

&#123;% block content %&#125;
&lt;div class="container"&gt;
    &lt;h1&gt;欢迎来到Flask应用&lt;/h1&gt;

    &#123;% if posts %&#125;
        &lt;div class="posts"&gt;
            &#123;% for post in posts %&#125;
                &lt;article class="post"&gt;
                    &lt;h2&gt;&lt;a href="&#123;&#123; url_for('show_post', id=post.id) &#125;&#125;"&gt;&#123;&#123; post.title &#125;&#125;&lt;/a&gt;&lt;/h2&gt;
                    &lt;p class="meta"&gt;
                        作者：&#123;&#123; post.author.username &#125;&#125; |
                        时间：&#123;&#123; moment(post.timestamp).format('YYYY-MM-DD HH:mm') &#125;&#125;
                    &lt;/p&gt;
                    &lt;div class="content"&gt;
                        &#123;&#123; post.body|truncate(200)|safe &#125;&#125;
                    &lt;/div&gt;
                &lt;/article&gt;
            &#123;% endfor %&#125;
        &lt;/div&gt;

        &#123;% if pagination %&#125;
            &lt;div class="pagination"&gt;
                &#123;% if pagination.has_prev %&#125;
                    &lt;a href="&#123;&#123; url_for('index', page=pagination.prev_num) &#125;&#125;"&gt;上一页&lt;/a&gt;
                &#123;% endif %&#125;

                &#123;% for page_num in pagination.iter_pages() %&#125;
                    &#123;% if page_num %&#125;
                        &#123;% if page_num != pagination.page %&#125;
                            &lt;a href="&#123;&#123; url_for('index', page=page_num) &#125;&#125;"&gt;&#123;&#123; page_num &#125;&#125;&lt;/a&gt;
                        &#123;% else %&#125;
                            &lt;strong&gt;&#123;&#123; page_num &#125;&#125;&lt;/strong&gt;
                        &#123;% endif %&#125;
                    &#123;% else %&#125;
                        &lt;span&gt;...&lt;/span&gt;
                    &#123;% endif %&#125;
                &#123;% endfor %&#125;

                &#123;% if pagination.has_next %&#125;
                    &lt;a href="&#123;&#123; url_for('index', page=pagination.next_num) &#125;&#125;"&gt;下一页&lt;/a&gt;
                &#123;% endif %&#125;
            &lt;/div&gt;
        &#123;% endif %&#125;
    &#123;% else %&#125;
        &lt;p&gt;暂无文章&lt;/p&gt;
    &#123;% endif %&#125;
&lt;/div&gt;
&#123;% endblock %&#125;</code></pre>
              </div>
            </div>
          </div>

          <div>
            <h3 class="text-xl font-bold text-pink-800 mb-4">模板过滤器和函数</h3>
            <div class="space-y-4">
              <div class="bg-green-50 p-3 rounded border border-green-200">
                <strong class="text-green-700">自定义过滤器：</strong>
                <pre class="bg-white p-2 rounded mt-2 text-xs"><code># app/filters.py
from flask import current_app
import datetime

@current_app.template_filter('datetime')
def datetime_filter(value, format='%Y-%m-%d %H:%M'):
    """格式化日期时间"""
    if isinstance(value, datetime.datetime):
        return value.strftime(format)
    return value

@current_app.template_filter('markdown')
def markdown_filter(text):
    """Markdown转HTML"""
    import markdown
    return markdown.markdown(text, extensions=['codehilite'])

@current_app.template_filter('truncate_html')
def truncate_html_filter(text, length=255):
    """截断HTML内容"""
    from bs4 import BeautifulSoup
    soup = BeautifulSoup(text, 'html.parser')
    text_content = soup.get_text()
    if len(text_content) &lt;= length:
        return text
    return text_content[:length] + '...'

# 自定义模板函数
@current_app.template_global()
def get_current_year():
    """获取当前年份"""
    return datetime.datetime.now().year

@current_app.template_global()
def url_for_other_page(page):
    """生成分页URL"""
    args = request.view_args.copy()
    args['page'] = page
    return url_for(request.endpoint, **args)

# 在模板中使用
# &#123;&#123; post.timestamp|datetime &#125;&#125;
# &#123;&#123; post.content|markdown|safe &#125;&#125;
# &#123;&#123; post.body|truncate_html(100) &#125;&#125;
# &#123;&#123; get_current_year() &#125;&#125;</code></pre>
              </div>

              <div class="bg-yellow-50 p-3 rounded border border-yellow-200">
                <strong class="text-yellow-700">模板上下文处理器：</strong>
                <pre class="bg-white p-2 rounded mt-2 text-xs"><code># app/__init__.py
@app.context_processor
def inject_conf_vars():
    """注入配置变量到模板"""
    return dict(
        SITE_NAME=app.config.get('SITE_NAME', 'Flask App'),
        SITE_URL=app.config.get('SITE_URL', 'http://localhost:5000'),
        DEBUG=app.config.get('DEBUG', False)
    )

@app.context_processor
def utility_processor():
    """注入工具函数到模板"""
    def format_price(amount, currency='¥'):
        return f'{currency}{amount:.2f}'

    def is_mobile():
        return request.user_agent.platform in ['android', 'iphone']

    return dict(
        format_price=format_price,
        is_mobile=is_mobile
    )

# 在模板中使用
# &#123;&#123; SITE_NAME &#125;&#125;
# &#123;&#123; format_price(product.price) &#125;&#125;
# &#123;% if is_mobile() %&#125;移动端内容&#123;% endif %&#125;</code></pre>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Flask蓝图系统 -->
      <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-white/50 mb-8">
        <h2 class="text-3xl font-bold text-cyan-800 mb-6 flex items-center">
          <svg class="w-8 h-8 mr-3 text-cyan-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
          </svg>
          Flask 蓝图系统
        </h2>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-bold text-cyan-800 mb-4">蓝图定义</h3>
            <div class="space-y-4">
              <div class="bg-cyan-50 p-3 rounded border border-cyan-200">
                <strong class="text-cyan-700">认证蓝图：</strong>
                <pre class="bg-white p-2 rounded mt-2 text-xs"><code># app/auth/__init__.py
from flask import Blueprint

bp = Blueprint('auth', __name__)

from app.auth import routes

# app/auth/routes.py
from flask import render_template, redirect, url_for, flash, request
from flask_login import login_user, logout_user, current_user
from app.auth import bp
from app.auth.forms import LoginForm, RegistrationForm
from app.models import User
from app import db

@bp.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('main.index'))

    form = LoginForm()
    if form.validate_on_submit():
        user = User.query.filter_by(username=form.username.data).first()
        if user and user.check_password(form.password.data):
            login_user(user, remember=form.remember_me.data)
            next_page = request.args.get('next')
            if not next_page or url_parse(next_page).netloc != '':
                next_page = url_for('main.index')
            return redirect(next_page)
        flash('用户名或密码错误')

    return render_template('auth/login.html', title='登录', form=form)

@bp.route('/logout')
def logout():
    logout_user()
    return redirect(url_for('main.index'))

@bp.route('/register', methods=['GET', 'POST'])
def register():
    if current_user.is_authenticated:
        return redirect(url_for('main.index'))

    form = RegistrationForm()
    if form.validate_on_submit():
        user = User(username=form.username.data, email=form.email.data)
        user.set_password(form.password.data)
        db.session.add(user)
        db.session.commit()
        flash('注册成功!')
        return redirect(url_for('auth.login'))

    return render_template('auth/register.html', title='注册', form=form)</code></pre>
              </div>
            </div>
          </div>

          <div>
            <h3 class="text-xl font-bold text-cyan-800 mb-4">蓝图注册</h3>
            <div class="space-y-4">
              <div class="bg-blue-50 p-3 rounded border border-blue-200">
                <strong class="text-blue-700">应用工厂中注册蓝图：</strong>
                <pre class="bg-white p-2 rounded mt-2 text-xs"><code># app/__init__.py
def create_app(config_class=Config):
    app = Flask(__name__)
    app.config.from_object(config_class)

    # 初始化扩展
    db.init_app(app)
    migrate.init_app(app, db)
    login.init_app(app)

    # 注册蓝图
    from app.main import bp as main_bp
    app.register_blueprint(main_bp)

    from app.auth import bp as auth_bp
    app.register_blueprint(auth_bp, url_prefix='/auth')

    from app.api import bp as api_bp
    app.register_blueprint(api_bp, url_prefix='/api/v1')

    from app.admin import bp as admin_bp
    app.register_blueprint(admin_bp, url_prefix='/admin')

    from app.errors import bp as errors_bp
    app.register_blueprint(errors_bp)

    return app

# app/main/__init__.py
from flask import Blueprint

bp = Blueprint('main', __name__)

from app.main import routes

# app/api/__init__.py
from flask import Blueprint

bp = Blueprint('api', __name__)

from app.api import routes

# 蓝图中的错误处理
@bp.errorhandler(404)
def not_found_error(error):
    return render_template('errors/404.html'), 404

@bp.errorhandler(500)
def internal_error(error):
    db.session.rollback()
    return render_template('errors/500.html'), 500</code></pre>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FlaskBasics'
}
</script>

<style scoped>
@keyframes blob {
  0% { transform: translate(0px, 0px) scale(1); }
  33% { transform: translate(30px, -50px) scale(1.1); }
  66% { transform: translate(-20px, 20px) scale(0.9); }
  100% { transform: translate(0px, 0px) scale(1); }
}

.animate-blob {
  animation: blob 7s infinite;
}

.animation-delay-2000 {
  animation-delay: 2s;
}
</style>
