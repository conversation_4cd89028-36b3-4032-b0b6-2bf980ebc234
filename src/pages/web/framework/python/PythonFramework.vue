<template>
  <div class="min-h-screen bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50 flex flex-col relative">
    <!-- 装饰性背景 -->
    <div class="absolute top-0 left-0 w-full h-40 bg-gradient-to-r from-green-400/10 to-emerald-400/10 transform -skew-y-1"></div>
    
    <div class="max-w-7xl mx-auto py-12 px-4 sm:px-6 md:px-8 w-full relative z-10">
      <!-- 页面头部 -->
      <div class="text-center mb-12">
        <div class="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-green-500 to-emerald-600 rounded-full mb-6 shadow-lg">
          <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 24 24">
            <path d="M14.25.18l.9.2.73.26.59.3.45.32.34.34.25.34.16.33.1.3.04.26.02.2-.01.13V8.5l-.05.63-.13.55-.21.46-.26.38-.3.31-.33.25-.35.19-.35.14-.33.1-.3.07-.26.04-.21.02H8.77l-.69.05-.59.14-.5.22-.41.27-.33.32-.27.35-.2.36-.15.37-.1.35-.07.32-.04.27-.02.21v3.06H3.17l-.21-.03-.28-.07-.32-.12-.35-.18-.36-.26-.36-.36-.35-.46-.32-.59-.28-.73-.21-.88-.14-1.05-.05-1.23.06-1.22.16-1.04.24-.87.32-.71.36-.57.4-.44.42-.33.42-.24.4-.16.36-.1.32-.05.26-.02.21-.01h5.84l.69-.05.59-.14.5-.21.41-.28.33-.32.27-.35.2-.36.15-.36.1-.35.07-.32.04-.28.02-.21V6.07h2.09l.14.01zm-6.47 14.25l-.23.33-.***********.***********.41.09.41-.09.33-.22.23-.34.08-.41-.08-.41-.23-.33-.33-.22-.41-.09-.41.09-.33.22zM21.1 27.2l.13-.07.15-.13.15-.2.13-.27.04-.31-.04-.32-.13-.27-.15-.2-.15-.13-.13-.07-.14-.03-.14.03-.14.07-.15.13-.15.2-.13.27-.***********.**********.***********.14.03.14-.03z"/>
          </svg>
        </div>
        <h1 class="text-4xl md:text-5xl font-extrabold text-transparent bg-clip-text bg-gradient-to-r from-green-600 to-emerald-600 mb-4">
          Python 框架安全
        </h1>
        <p class="text-xl text-gray-700 max-w-4xl mx-auto leading-relaxed mb-6">
          🐍 深入学习Python生态系统中主流框架的安全漏洞与防护技术
        </p>
        <div class="flex flex-wrap justify-center gap-3 text-sm">
          <span class="bg-green-100 text-green-700 px-3 py-1 rounded-full font-medium">Django 安全</span>
          <span class="bg-emerald-100 text-emerald-700 px-3 py-1 rounded-full font-medium">Flask 防护</span>
          <span class="bg-teal-100 text-teal-700 px-3 py-1 rounded-full font-medium">FastAPI 配置</span>
          <span class="bg-cyan-100 text-cyan-700 px-3 py-1 rounded-full font-medium">Tornado 安全</span>
        </div>
      </div>

      <!-- Python框架分类 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 w-full max-w-6xl mx-auto mb-16">
        <!-- Django 框架 -->
        <router-link to="/web/framework/python/django" class="group relative overflow-hidden rounded-2xl bg-gradient-to-br from-green-600 to-emerald-700 p-8 text-white transition-all duration-300 hover:scale-105 hover:shadow-2xl">
          <div class="absolute inset-0 bg-gradient-to-br from-green-500/20 to-emerald-600/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          <div class="relative z-10">
            <div class="flex items-center mb-4">
              <div class="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center mr-4">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"/>
                </svg>
              </div>
              <h3 class="text-2xl font-bold">Django 框架</h3>
            </div>
            <p class="text-green-100 mb-4">Django安全中间件、ORM防护、CSRF保护与权限系统</p>
            <div class="flex flex-wrap gap-2">
              <span class="px-3 py-1 bg-white/20 rounded-full text-sm">Django ORM</span>
              <span class="px-3 py-1 bg-white/20 rounded-full text-sm">中间件</span>
              <span class="px-3 py-1 bg-white/20 rounded-full text-sm">权限系统</span>
            </div>
          </div>
        </router-link>

        <!-- Flask 框架 -->
        <router-link to="/web/framework/python/flask" class="group">
          <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/50 hover:shadow-2xl transition-all duration-300 group-hover:scale-105 h-64 flex flex-col">
            <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mb-4 group-hover:from-blue-600 group-hover:to-blue-700 transition-all duration-300">
              <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"/>
              </svg>
            </div>
            <h3 class="text-xl font-bold text-gray-800 mb-2">Flask 框架</h3>
            <p class="text-gray-600 text-sm flex-1">
              轻量级微框架，灵活的路由系统和模板引擎。
              深入分析Flask的安全机制、SSTI漏洞和防护方案。
            </p>
            <div class="flex flex-wrap gap-2">
              <span class="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full">SSTI</span>
              <span class="px-2 py-1 bg-indigo-100 text-indigo-700 text-xs rounded-full">Session安全</span>
              <span class="px-2 py-1 bg-purple-100 text-purple-700 text-xs rounded-full">蓝图安全</span>
            </div>
          </div>
        </router-link>

        <!-- FastAPI 框架 -->
        <div class="group relative overflow-hidden rounded-2xl bg-gradient-to-br from-gray-400 to-gray-500 p-8 text-white cursor-not-allowed opacity-75">
          <div class="absolute inset-0 bg-gradient-to-br from-gray-300/20 to-gray-400/20"></div>
          <div class="relative z-10">
            <div class="flex items-center mb-4">
              <div class="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center mr-4">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
              </div>
              <h3 class="text-2xl font-bold">FastAPI 框架</h3>
              <span class="ml-3 px-2 py-1 bg-orange-500 text-white text-xs rounded-full">即将上线</span>
            </div>
            <p class="text-gray-100 mb-4">FastAPI现代API框架安全、OAuth2认证与数据验证</p>
            <div class="flex flex-wrap gap-2">
              <span class="px-3 py-1 bg-white/20 rounded-full text-sm">OAuth2</span>
              <span class="px-3 py-1 bg-white/20 rounded-full text-sm">数据验证</span>
              <span class="px-3 py-1 bg-white/20 rounded-full text-sm">API安全</span>
            </div>
          </div>
        </div>

        <!-- Tornado 框架 -->
        <div class="group relative overflow-hidden rounded-2xl bg-gradient-to-br from-gray-400 to-gray-500 p-8 text-white cursor-not-allowed opacity-75">
          <div class="absolute inset-0 bg-gradient-to-br from-gray-300/20 to-gray-400/20"></div>
          <div class="relative z-10">
            <div class="flex items-center mb-4">
              <div class="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center mr-4">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
              </div>
              <h3 class="text-2xl font-bold">Tornado 框架</h3>
              <span class="ml-3 px-2 py-1 bg-orange-500 text-white text-xs rounded-full">即将上线</span>
            </div>
            <p class="text-gray-100 mb-4">Tornado异步框架安全配置、WebSocket安全与性能优化</p>
            <div class="flex flex-wrap gap-2">
              <span class="px-3 py-1 bg-white/20 rounded-full text-sm">异步安全</span>
              <span class="px-3 py-1 bg-white/20 rounded-full text-sm">WebSocket</span>
              <span class="px-3 py-1 bg-white/20 rounded-full text-sm">性能优化</span>
            </div>
          </div>
        </div>

        <!-- Pyramid 框架 -->
        <div class="group relative overflow-hidden rounded-2xl bg-gradient-to-br from-gray-400 to-gray-500 p-8 text-white cursor-not-allowed opacity-75">
          <div class="absolute inset-0 bg-gradient-to-br from-gray-300/20 to-gray-400/20"></div>
          <div class="relative z-10">
            <div class="flex items-center mb-4">
              <div class="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center mr-4">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
              </div>
              <h3 class="text-2xl font-bold">Pyramid 框架</h3>
              <span class="ml-3 px-2 py-1 bg-orange-500 text-white text-xs rounded-full">即将上线</span>
            </div>
            <p class="text-gray-100 mb-4">Pyramid灵活框架安全配置、权限控制与视图保护</p>
            <div class="flex flex-wrap gap-2">
              <span class="px-3 py-1 bg-white/20 rounded-full text-sm">权限控制</span>
              <span class="px-3 py-1 bg-white/20 rounded-full text-sm">视图保护</span>
              <span class="px-3 py-1 bg-white/20 rounded-full text-sm">安全策略</span>
            </div>
          </div>
        </div>

        <!-- Sanic 框架 -->
        <div class="group relative overflow-hidden rounded-2xl bg-gradient-to-br from-gray-400 to-gray-500 p-8 text-white cursor-not-allowed opacity-75">
          <div class="absolute inset-0 bg-gradient-to-br from-gray-300/20 to-gray-400/20"></div>
          <div class="relative z-10">
            <div class="flex items-center mb-4">
              <div class="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center mr-4">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
              </div>
              <h3 class="text-2xl font-bold">Sanic 框架</h3>
              <span class="ml-3 px-2 py-1 bg-orange-500 text-white text-xs rounded-full">即将上线</span>
            </div>
            <p class="text-gray-100 mb-4">Sanic高性能异步框架安全配置与中间件防护</p>
            <div class="flex flex-wrap gap-2">
              <span class="px-3 py-1 bg-white/20 rounded-full text-sm">异步处理</span>
              <span class="px-3 py-1 bg-white/20 rounded-full text-sm">中间件</span>
              <span class="px-3 py-1 bg-white/20 rounded-full text-sm">高性能</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 返回按钮 -->
      <div class="text-center mt-12">
        <router-link to="/web/framework" class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-green-500 to-emerald-600 text-white font-medium rounded-xl hover:from-green-600 hover:to-emerald-700 transition-all duration-300 shadow-lg hover:shadow-xl">
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
          </svg>
          返回框架安全
        </router-link>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PythonFrameworkSecurity',
  mounted() {
    // 页面加载动画
    this.$nextTick(() => {
      const cards = document.querySelectorAll('.group');
      cards.forEach((card, index) => {
        setTimeout(() => {
          card.style.opacity = '1';
          card.style.transform = 'translateY(0)';
        }, index * 100);
      });
    });
  }
}
</script>

<style scoped>
.group {
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.6s ease;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, #10b981, #059669);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, #059669, #047857);
}
</style>