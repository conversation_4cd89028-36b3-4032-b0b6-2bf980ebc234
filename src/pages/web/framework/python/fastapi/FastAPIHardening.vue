<template>
  <div class="min-h-screen bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50 flex flex-col relative">
    <!-- 背景装饰 -->
    <div class="absolute inset-0 overflow-hidden pointer-events-none">
      <div class="absolute -top-40 -right-40 w-80 h-80 bg-green-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob"></div>
      <div class="absolute -bottom-40 -left-40 w-80 h-80 bg-emerald-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000"></div>
    </div>

    <div class="max-w-7xl mx-auto py-12 px-4 sm:px-6 md:px-8 w-full relative z-10">
      <!-- 返回按钮 -->
      <div class="mb-8">
        <router-link to="/web/framework/python/fastapi" class="inline-flex items-center text-green-600 hover:text-green-700 transition-colors duration-200">
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
          </svg>
          返回 FastAPI 框架
        </router-link>
      </div>

      <!-- 页面头部 -->
      <div class="text-center mb-12">
        <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-600 rounded-full mb-6 shadow-lg">
          <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"/>
          </svg>
        </div>
        <h1 class="text-4xl font-extrabold text-transparent bg-clip-text bg-gradient-to-r from-green-600 to-emerald-600 mb-4">
          FastAPI 安全加固
        </h1>
        <p class="text-xl text-gray-700 max-w-4xl mx-auto leading-relaxed">
          🛡️ 全面的FastAPI应用安全加固方案和最佳实践
        </p>
      </div>

      <!-- 基础安全配置 -->
      <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-white/50 mb-8">
        <h2 class="text-3xl font-bold text-green-800 mb-6 flex items-center">
          <svg class="w-8 h-8 mr-3 text-green-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
            <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
          </svg>
          基础安全配置
        </h2>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-bold text-green-800 mb-4">生产环境配置</h3>
            <div class="space-y-4">
              <div class="bg-red-50 p-4 rounded-lg border border-red-200">
                <h4 class="font-bold text-red-700 mb-2">安全配置</h4>
                <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code># app/core/config.py - 生产环境安全配置
from pydantic import BaseSettings, validator
import secrets
import os

class ProductionSettings(BaseSettings):
    """生产环境安全配置"""
    
    # 基础安全配置
    DEBUG: bool = False
    TESTING: bool = False
    
    # 强制使用强密钥
    SECRET_KEY: str = Field(..., min_length=32)
    
    @validator('SECRET_KEY')
    def validate_secret_key(cls, v):
        if len(v) < 32:
            raise ValueError('SECRET_KEY must be at least 32 characters')
        if v in ['secret', 'password', '123456', 'admin']:
            raise ValueError('SECRET_KEY cannot be a common password')
        return v
    
    # JWT安全配置
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 15  # 短过期时间
    REFRESH_TOKEN_EXPIRE_MINUTES: int = 60 * 24  # 1天
    
    # 禁用API文档
    DOCS_ENABLED: bool = False
    OPENAPI_URL: Optional[str] = None
    DOCS_URL: Optional[str] = None
    REDOC_URL: Optional[str] = None
    
    # 严格的CORS配置
    BACKEND_CORS_ORIGINS: List[str] = []  # 明确指定允许的域名
    
    # 数据库安全配置
    DATABASE_URL: str = Field(..., regex=r'^postgresql://.*')
    DATABASE_ECHO: bool = False  # 禁用SQL日志
    
    # 文件上传限制
    MAX_UPLOAD_SIZE: int = 5 * 1024 * 1024  # 5MB
    ALLOWED_EXTENSIONS: List[str] = ["jpg", "jpeg", "png", "pdf"]
    
    # 限流配置
    RATE_LIMIT_ENABLED: bool = True
    RATE_LIMIT_REQUESTS: int = 60  # 每分钟60次请求
    RATE_LIMIT_PERIOD: int = 60
    
    # 安全头配置
    SECURITY_HEADERS_ENABLED: bool = True
    
    class Config:
        env_file = ".env.production"
        case_sensitive = True

# 应用工厂安全配置
def create_secure_app() -> FastAPI:
    """创建安全的FastAPI应用"""
    
    settings = ProductionSettings()
    
    app = FastAPI(
        title=settings.PROJECT_NAME,
        description=settings.PROJECT_DESCRIPTION,
        version=settings.VERSION,
        # 生产环境禁用文档
        docs_url=None,
        redoc_url=None,
        openapi_url=None,
        # 禁用默认异常处理器
        debug=False
    )
    
    # 添加安全中间件
    setup_security_middleware(app, settings)
    
    return app</code></pre>
              </div>
            </div>
          </div>

          <div>
            <h3 class="text-xl font-bold text-green-800 mb-4">安全中间件</h3>
            <div class="space-y-4">
              <div class="bg-blue-50 p-4 rounded-lg border border-blue-200">
                <h4 class="font-bold text-blue-700 mb-2">安全头中间件</h4>
                <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code># app/middleware/security.py
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.requests import Request
from starlette.responses import Response

class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """安全头中间件"""
    
    async def dispatch(self, request: Request, call_next):
        response = await call_next(request)
        
        # 安全头配置
        security_headers = {
            # 防止XSS攻击
            "X-Content-Type-Options": "nosniff",
            "X-Frame-Options": "DENY",
            "X-XSS-Protection": "1; mode=block",
            
            # HSTS
            "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload",
            
            # 引用策略
            "Referrer-Policy": "strict-origin-when-cross-origin",
            
            # 权限策略
            "Permissions-Policy": "geolocation=(), microphone=(), camera=()",
            
            # 内容安全策略
            "Content-Security-Policy": (
                "default-src 'self'; "
                "script-src 'self'; "
                "style-src 'self' 'unsafe-inline'; "
                "img-src 'self' data: https:; "
                "font-src 'self'; "
                "connect-src 'self'; "
                "frame-ancestors 'none'; "
                "base-uri 'self'; "
                "form-action 'self';"
            ),
            
            # 移除服务器信息
            "Server": "FastAPI"
        }
        
        # 添加安全头
        for header, value in security_headers.items():
            response.headers[header] = value
        
        # 移除敏感信息
        response.headers.pop("X-Powered-By", None)
        
        return response

class RateLimitMiddleware(BaseHTTPMiddleware):
    """限流中间件"""
    
    def __init__(self, app, calls: int = 60, period: int = 60):
        super().__init__(app)
        self.calls = calls
        self.period = period
        self.clients = {}
        
    async def dispatch(self, request: Request, call_next):
        client_ip = request.client.host
        current_time = time.time()
        
        # 清理过期记录
        if client_ip in self.clients:
            self.clients[client_ip] = [
                timestamp for timestamp in self.clients[client_ip]
                if current_time - timestamp < self.period
            ]
        else:
            self.clients[client_ip] = []
        
        # 检查限流
        if len(self.clients[client_ip]) >= self.calls:
            return Response(
                content=json.dumps({"detail": "Rate limit exceeded"}),
                status_code=429,
                headers={
                    "Content-Type": "application/json",
                    "Retry-After": str(self.period)
                }
            )
        
        # 记录请求时间
        self.clients[client_ip].append(current_time)
        
        return await call_next(request)

def setup_security_middleware(app: FastAPI, settings):
    """设置安全中间件"""
    
    # 安全头中间件
    app.add_middleware(SecurityHeadersMiddleware)
    
    # 限流中间件
    if settings.RATE_LIMIT_ENABLED:
        app.add_middleware(
            RateLimitMiddleware,
            calls=settings.RATE_LIMIT_REQUESTS,
            period=settings.RATE_LIMIT_PERIOD
        )
    
    # CORS中间件（严格配置）
    if settings.BACKEND_CORS_ORIGINS:
        app.add_middleware(
            CORSMiddleware,
            allow_origins=settings.BACKEND_CORS_ORIGINS,
            allow_credentials=False,  # 禁用凭据
            allow_methods=["GET", "POST", "PUT", "DELETE"],
            allow_headers=["Authorization", "Content-Type"],
            expose_headers=[],
        )
    
    # 受信任主机中间件
    if settings.ALLOWED_HOSTS:
        app.add_middleware(
            TrustedHostMiddleware,
            allowed_hosts=settings.ALLOWED_HOSTS
        )</code></pre>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 依赖注入安全 -->
      <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-white/50 mb-8">
        <h2 class="text-3xl font-bold text-emerald-800 mb-6 flex items-center">
          <svg class="w-8 h-8 mr-3 text-emerald-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"/>
          </svg>
          依赖注入安全加固
        </h2>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-bold text-emerald-800 mb-4">安全的依赖注入</h3>
            <div class="space-y-4">
              <div class="bg-emerald-50 p-3 rounded border border-emerald-200">
                <strong class="text-emerald-700">安全依赖实现：</strong>
                <pre class="bg-white p-2 rounded mt-2 text-xs"><code># app/dependencies.py - 安全的依赖注入
from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
import re
import os
from pathlib import Path

security = HTTPBearer()

# 安全的文件操作依赖
def secure_file_reader(filename: str = None):
    """安全的文件读取依赖"""
    if not filename:
        raise HTTPException(status_code=400, detail="Filename required")
    
    # 输入验证
    if not re.match(r'^[a-zA-Z0-9._-]+$', filename):
        raise HTTPException(status_code=400, detail="Invalid filename")
    
    # 路径遍历防护
    if '..' in filename or filename.startswith('/'):
        raise HTTPException(status_code=400, detail="Path traversal detected")
    
    # 限制文件扩展名
    allowed_extensions = ['.txt', '.json', '.csv']
    if not any(filename.endswith(ext) for ext in allowed_extensions):
        raise HTTPException(status_code=400, detail="File type not allowed")
    
    # 构造安全路径
    safe_dir = Path("data")
    file_path = safe_dir / filename
    
    # 确保路径在安全目录内
    try:
        file_path.resolve().relative_to(safe_dir.resolve())
    except ValueError:
        raise HTTPException(status_code=400, detail="Access denied")
    
    # 检查文件是否存在
    if not file_path.exists():
        raise HTTPException(status_code=404, detail="File not found")
    
    try:
        with open(file_path, 'r') as f:
            return f.read()
    except Exception as e:
        raise HTTPException(status_code=500, detail="File read error")

# 安全的命令执行依赖（避免使用）
def secure_command_validator(command: str = None):
    """安全的命令验证（建议避免命令执行）"""
    if not command:
        raise HTTPException(status_code=400, detail="Command required")
    
    # 白名单命令
    allowed_commands = ['date', 'uptime', 'whoami']
    
    if command not in allowed_commands:
        raise HTTPException(status_code=400, detail="Command not allowed")
    
    return command

# 安全的权限检查依赖
def require_admin_role(current_user: dict = Depends(get_current_user)):
    """要求管理员权限"""
    if not current_user:
        raise HTTPException(status_code=401, detail="Authentication required")
    
    if current_user.get('role') != 'admin':
        raise HTTPException(status_code=403, detail="Admin role required")
    
    return current_user

def require_permission(permission: str):
    """要求特定权限"""
    def permission_checker(current_user: dict = Depends(get_current_user)):
        if not current_user:
            raise HTTPException(status_code=401, detail="Authentication required")
        
        user_permissions = current_user.get('permissions', [])
        if permission not in user_permissions:
            raise HTTPException(
                status_code=403, 
                detail=f"Permission '{permission}' required"
            )
        
        return current_user
    
    return permission_checker

# 安全的数据库依赖
def get_secure_db():
    """安全的数据库会话"""
    db = SessionLocal()
    try:
        # 设置数据库会话安全选项
        db.execute("SET SESSION sql_mode = 'STRICT_TRANS_TABLES'")
        yield db
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail="Database error")
    finally:
        db.close()</code></pre>
              </div>
            </div>
          </div>

          <div>
            <h3 class="text-xl font-bold text-emerald-800 mb-4">输入验证和清理</h3>
            <div class="space-y-4">
              <div class="bg-blue-50 p-3 rounded border border-blue-200">
                <strong class="text-blue-700">输入验证装饰器：</strong>
                <pre class="bg-white p-2 rounded mt-2 text-xs"><code># app/validators.py - 输入验证
import re
from functools import wraps
from fastapi import HTTPException

def validate_input(
    max_length: int = 1000,
    allowed_chars: str = None,
    forbidden_patterns: list = None
):
    """输入验证装饰器"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 验证所有字符串参数
            for key, value in kwargs.items():
                if isinstance(value, str):
                    # 长度检查
                    if len(value) > max_length:
                        raise HTTPException(
                            status_code=400,
                            detail=f"Parameter '{key}' too long"
                        )
                    
                    # 字符检查
                    if allowed_chars and not re.match(allowed_chars, value):
                        raise HTTPException(
                            status_code=400,
                            detail=f"Invalid characters in '{key}'"
                        )
                    
                    # 危险模式检查
                    default_forbidden = [
                        r'<script.*?>',
                        r'javascript:',
                        r'vbscript:',
                        r'on\w+\s*=',
                        r'\.\./',
                        r'/etc/passwd',
                        r'cmd\.exe',
                        r'powershell',
                        r'eval\s*\(',
                        r'exec\s*\(',
                        r'system\s*\(',
                        r'__import__',
                        r'subprocess',
                    ]
                    
                    patterns = forbidden_patterns or default_forbidden
                    
                    for pattern in patterns:
                        if re.search(pattern, value, re.IGNORECASE):
                            raise HTTPException(
                                status_code=400,
                                detail=f"Dangerous pattern detected in '{key}'"
                            )
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator

# 使用示例
@app.post("/process")
@validate_input(
    max_length=500,
    allowed_chars=r'^[a-zA-Z0-9\s\-_.]+$',
    forbidden_patterns=[r'admin', r'root', r'password']
)
async def process_data(data: str):
    return {"processed": data}

# SQL注入防护
def sanitize_sql_input(value: str) -> str:
    """SQL输入清理"""
    if not isinstance(value, str):
        return value
    
    # 移除危险字符
    dangerous_chars = ["'", '"', ';', '--', '/*', '*/', 'xp_', 'sp_']
    
    for char in dangerous_chars:
        value = value.replace(char, '')
    
    return value

# XSS防护
def sanitize_html_input(value: str) -> str:
    """HTML输入清理"""
    if not isinstance(value, str):
        return value
    
    import html
    
    # HTML实体编码
    value = html.escape(value)
    
    # 移除危险标签
    dangerous_tags = ['<script', '<iframe', '<object', '<embed', '<form']
    
    for tag in dangerous_tags:
        value = re.sub(tag, '', value, flags=re.IGNORECASE)
    
    return value</code></pre>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- JWT安全加固 -->
      <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-white/50 mb-8">
        <h2 class="text-3xl font-bold text-teal-800 mb-6 flex items-center">
          <svg class="w-8 h-8 mr-3 text-teal-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
          </svg>
          JWT 安全加固
        </h2>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-bold text-teal-800 mb-4">安全的JWT实现</h3>
            <div class="space-y-4">
              <div class="bg-teal-50 p-3 rounded border border-teal-200">
                <strong class="text-teal-700">JWT安全配置：</strong>
                <pre class="bg-white p-2 rounded mt-2 text-xs"><code># app/core/security.py - 安全的JWT实现
from jose import JWTError, jwt
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
import secrets
import hashlib

class SecureJWTManager:
    """安全的JWT管理器"""
    
    def __init__(self, settings):
        # 使用强密钥
        self.secret_key = settings.SECRET_KEY
        if len(self.secret_key) < 32:
            raise ValueError("JWT secret key must be at least 32 characters")
        
        # 只允许安全算法
        self.algorithm = "HS256"
        self.access_token_expire_minutes = settings.ACCESS_TOKEN_EXPIRE_MINUTES
        self.refresh_token_expire_minutes = settings.REFRESH_TOKEN_EXPIRE_MINUTES
        
        # Token黑名单（生产环境建议使用Redis）
        self.blacklisted_tokens = set()
    
    def create_access_token(self, data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
        """创建访问令牌"""
        to_encode = data.copy()
        
        # 设置过期时间
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=self.access_token_expire_minutes)
        
        # 添加标准声明
        to_encode.update({
            "exp": expire,
            "iat": datetime.utcnow(),
            "type": "access",
            "jti": secrets.token_urlsafe(16)  # JWT ID
        })
        
        # 签名
        encoded_jwt = jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)
        return encoded_jwt
    
    def create_refresh_token(self, data: Dict[str, Any]) -> str:
        """创建刷新令牌"""
        to_encode = data.copy()
        
        expire = datetime.utcnow() + timedelta(minutes=self.refresh_token_expire_minutes)
        
        to_encode.update({
            "exp": expire,
            "iat": datetime.utcnow(),
            "type": "refresh",
            "jti": secrets.token_urlsafe(16)
        })
        
        encoded_jwt = jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)
        return encoded_jwt
    
    def verify_token(self, token: str, token_type: str = "access") -> Optional[Dict[str, Any]]:
        """验证令牌"""
        try:
            # 检查黑名单
            if self._is_blacklisted(token):
                return None
            
            # 解码和验证
            payload = jwt.decode(
                token, 
                self.secret_key, 
                algorithms=[self.algorithm],
                options={
                    "verify_exp": True,
                    "verify_iat": True,
                    "verify_signature": True
                }
            )
            
            # 验证token类型
            if payload.get("type") != token_type:
                return None
            
            # 验证必需字段
            required_fields = ["exp", "iat", "jti"]
            if not all(field in payload for field in required_fields):
                return None
            
            return payload
            
        except JWTError:
            return None
    
    def blacklist_token(self, token: str):
        """将token加入黑名单"""
        # 生产环境建议使用Redis存储
        token_hash = hashlib.sha256(token.encode()).hexdigest()
        self.blacklisted_tokens.add(token_hash)
    
    def _is_blacklisted(self, token: str) -> bool:
        """检查token是否在黑名单中"""
        token_hash = hashlib.sha256(token.encode()).hexdigest()
        return token_hash in self.blacklisted_tokens
    
    def refresh_access_token(self, refresh_token: str) -> Optional[str]:
        """使用刷新令牌获取新的访问令牌"""
        payload = self.verify_token(refresh_token, "refresh")
        
        if not payload:
            return None
        
        # 创建新的访问令牌
        new_payload = {
            "sub": payload["sub"],
            "role": payload.get("role"),
            "permissions": payload.get("permissions", [])
        }
        
        return self.create_access_token(new_payload)

# 安全的认证依赖
jwt_manager = SecureJWTManager(settings)

async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """获取当前用户（安全版本）"""
    token = credentials.credentials
    
    # 验证token
    payload = jwt_manager.verify_token(token)
    if not payload:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # 获取用户信息
    username = payload.get("sub")
    if not username:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token payload"
        )
    
    # 从数据库获取用户（添加缓存）
    user = await get_user_by_username(username)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User not found"
        )
    
    # 检查用户状态
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User account is disabled"
        )
    
    return user</code></pre>
              </div>
            </div>
          </div>

          <div>
            <h3 class="text-xl font-bold text-teal-800 mb-4">Token管理和轮换</h3>
            <div class="space-y-4">
              <div class="bg-blue-50 p-3 rounded border border-blue-200">
                <strong class="text-blue-700">Token轮换机制：</strong>
                <pre class="bg-white p-2 rounded mt-2 text-xs"><code># app/api/auth.py - Token轮换
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm

router = APIRouter()

@router.post("/login")
async def login(form_data: OAuth2PasswordRequestForm = Depends()):
    """安全登录"""
    # 验证用户凭据
    user = await authenticate_user(form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # 检查用户状态
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User account is disabled"
        )
    
    # 创建token
    access_token = jwt_manager.create_access_token(
        data={
            "sub": user.username,
            "role": user.role,
            "permissions": user.permissions
        }
    )
    
    refresh_token = jwt_manager.create_refresh_token(
        data={"sub": user.username}
    )
    
    # 记录登录
    await log_user_login(user.id, request.client.host)
    
    return {
        "access_token": access_token,
        "refresh_token": refresh_token,
        "token_type": "bearer",
        "expires_in": settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60
    }

@router.post("/refresh")
async def refresh_token(refresh_token: str):
    """刷新访问令牌"""
    new_access_token = jwt_manager.refresh_access_token(refresh_token)
    
    if not new_access_token:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid refresh token"
        )
    
    return {
        "access_token": new_access_token,
        "token_type": "bearer",
        "expires_in": settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60
    }

@router.post("/logout")
async def logout(
    current_user: User = Depends(get_current_user),
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """安全登出"""
    # 将当前token加入黑名单
    jwt_manager.blacklist_token(credentials.credentials)
    
    # 记录登出
    await log_user_logout(current_user.id)
    
    return {"message": "Successfully logged out"}

@router.post("/logout-all")
async def logout_all_devices(current_user: User = Depends(get_current_user)):
    """登出所有设备"""
    # 更新用户的token版本或密钥
    await invalidate_all_user_tokens(current_user.id)
    
    return {"message": "Logged out from all devices"}

# Token安全检查中间件
class TokenSecurityMiddleware(BaseHTTPMiddleware):
    """Token安全检查中间件"""
    
    async def dispatch(self, request: Request, call_next):
        # 检查Authorization头
        auth_header = request.headers.get("Authorization")
        
        if auth_header and auth_header.startswith("Bearer "):
            token = auth_header.split(" ")[1]
            
            # 检查token格式
            if not self._is_valid_token_format(token):
                return Response(
                    content=json.dumps({"detail": "Invalid token format"}),
                    status_code=401,
                    headers={"Content-Type": "application/json"}
                )
            
            # 检查token长度（防止过长token攻击）
            if len(token) > 2048:
                return Response(
                    content=json.dumps({"detail": "Token too long"}),
                    status_code=401,
                    headers={"Content-Type": "application/json"}
                )
        
        return await call_next(request)
    
    def _is_valid_token_format(self, token: str) -> bool:
        """检查token格式"""
        # JWT应该有3个部分，用.分隔
        parts = token.split('.')
        if len(parts) != 3:
            return False
        
        # 检查每部分是否是有效的base64
        import base64
        
        for part in parts:
            try:
                # 添加padding
                padded = part + '=' * (4 - len(part) % 4)
                base64.urlsafe_b64decode(padded)
            except:
                return False
        
        return True</code></pre>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 数据验证安全 -->
      <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-white/50 mb-8">
        <h2 class="text-3xl font-bold text-cyan-800 mb-6 flex items-center">
          <svg class="w-8 h-8 mr-3 text-cyan-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
          </svg>
          Pydantic 数据验证安全
        </h2>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-bold text-cyan-800 mb-4">安全的数据模型</h3>
            <div class="space-y-4">
              <div class="bg-cyan-50 p-4 rounded-lg border border-cyan-200">
                <h4 class="font-bold text-cyan-700 mb-2">强化验证模型</h4>
                <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code># app/schemas/secure.py - 安全的Pydantic模型
from pydantic import BaseModel, validator, Field, root_validator
from typing import Optional, List, Dict, Any
import re
import html

class SecureBaseModel(BaseModel):
    """安全的基础模型"""

    class Config:
        # 严格模式
        extra = "forbid"  # 禁止额外字段
        validate_assignment = True  # 赋值时验证
        str_strip_whitespace = True  # 自动去除空白
        max_anystr_length = 1000  # 字符串最大长度

    @validator('*', pre=True)
    def sanitize_strings(cls, v):
        """清理所有字符串输入"""
        if isinstance(v, str):
            # HTML实体编码
            v = html.escape(v)

            # 移除危险字符
            dangerous_chars = ['<', '>', '"', "'", '&', '\x00']
            for char in dangerous_chars:
                v = v.replace(char, '')

            # 限制长度
            if len(v) > 1000:
                raise ValueError('String too long')

        return v

class SecureUserCreate(SecureBaseModel):
    """安全的用户创建模型"""
    username: str = Field(..., min_length=3, max_length=50, regex=r'^[a-zA-Z0-9_]+$')
    email: str = Field(..., regex=r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
    password: str = Field(..., min_length=8, max_length=128)
    full_name: Optional[str] = Field(None, max_length=100, regex=r'^[a-zA-Z\s]+$')

    @validator('username')
    def validate_username(cls, v):
        # 禁止保留用户名
        reserved_names = ['admin', 'root', 'system', 'api', 'www']
        if v.lower() in reserved_names:
            raise ValueError('Username is reserved')

        # 禁止SQL关键字
        sql_keywords = ['select', 'insert', 'update', 'delete', 'drop', 'union']
        if any(keyword in v.lower() for keyword in sql_keywords):
            raise ValueError('Username contains forbidden keywords')

        return v

    @validator('password')
    def validate_password(cls, v):
        # 密码复杂度检查
        if not re.search(r'[A-Z]', v):
            raise ValueError('Password must contain uppercase letter')
        if not re.search(r'[a-z]', v):
            raise ValueError('Password must contain lowercase letter')
        if not re.search(r'\d', v):
            raise ValueError('Password must contain digit')
        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', v):
            raise ValueError('Password must contain special character')

        # 禁止常见密码
        common_passwords = ['password', '123456', 'admin', 'qwerty']
        if v.lower() in common_passwords:
            raise ValueError('Password is too common')

        return v

    @validator('email')
    def validate_email(cls, v):
        # 禁止一次性邮箱域名
        disposable_domains = ['10minutemail.com', 'tempmail.org', 'guerrillamail.com']
        domain = v.split('@')[1].lower()
        if domain in disposable_domains:
            raise ValueError('Disposable email addresses not allowed')

        return v.lower()

class SecureFileUpload(SecureBaseModel):
    """安全的文件上传模型"""
    filename: str = Field(..., max_length=255)
    content_type: str = Field(..., max_length=100)
    size: int = Field(..., gt=0, le=10*1024*1024)  # 最大10MB

    @validator('filename')
    def validate_filename(cls, v):
        # 文件名安全检查
        if not re.match(r'^[a-zA-Z0-9._-]+$', v):
            raise ValueError('Invalid filename characters')

        # 禁止危险扩展名
        dangerous_extensions = [
            '.exe', '.bat', '.cmd', '.com', '.pif', '.scr', '.vbs', '.js',
            '.jar', '.php', '.asp', '.aspx', '.jsp', '.py', '.rb', '.pl'
        ]

        file_ext = '.' + v.split('.')[-1].lower() if '.' in v else ''
        if file_ext in dangerous_extensions:
            raise ValueError('File type not allowed')

        # 允许的扩展名
        allowed_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.pdf', '.txt', '.csv']
        if file_ext not in allowed_extensions:
            raise ValueError('File type not supported')

        return v

    @validator('content_type')
    def validate_content_type(cls, v):
        allowed_types = [
            'image/jpeg', 'image/png', 'image/gif',
            'application/pdf', 'text/plain', 'text/csv'
        ]

        if v not in allowed_types:
            raise ValueError('Content type not allowed')

        return v

class SecureSearchQuery(SecureBaseModel):
    """安全的搜索查询模型"""
    query: str = Field(..., min_length=1, max_length=200)
    filters: Optional[Dict[str, Any]] = Field(default_factory=dict)
    sort_by: Optional[str] = Field(None, regex=r'^[a-zA-Z_]+$')
    sort_order: Optional[str] = Field('asc', regex=r'^(asc|desc)$')
    page: int = Field(1, ge=1, le=1000)
    limit: int = Field(10, ge=1, le=100)

    @validator('query')
    def validate_query(cls, v):
        # 禁止SQL注入模式
        sql_patterns = [
            r'union\s+select', r'drop\s+table', r'delete\s+from',
            r'insert\s+into', r'update\s+set', r'exec\s*\(',
            r'script\s*:', r'javascript\s*:', r'vbscript\s*:'
        ]

        for pattern in sql_patterns:
            if re.search(pattern, v, re.IGNORECASE):
                raise ValueError('Query contains forbidden patterns')

        return v

    @validator('filters')
    def validate_filters(cls, v):
        if not v:
            return v

        # 限制过滤器数量
        if len(v) > 10:
            raise ValueError('Too many filters')

        # 验证过滤器键名
        allowed_filter_keys = ['category', 'status', 'date_from', 'date_to', 'author']

        for key in v.keys():
            if key not in allowed_filter_keys:
                raise ValueError(f'Filter key "{key}" not allowed')

        return v</code></pre>
              </div>
            </div>
          </div>

          <div>
            <h3 class="text-xl font-bold text-cyan-800 mb-4">自定义验证器</h3>
            <div class="space-y-4">
              <div class="bg-blue-50 p-4 rounded-lg border border-blue-200">
                <h4 class="font-bold text-blue-700 mb-2">高级验证技术</h4>
                <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code># app/validators/custom.py - 自定义验证器
from pydantic import validator
import re
import ipaddress
from datetime import datetime, date

class AdvancedValidators:
    """高级验证器集合"""

    @staticmethod
    def validate_ip_address(v: str) -> str:
        """IP地址验证"""
        try:
            ipaddress.ip_address(v)
            return v
        except ValueError:
            raise ValueError('Invalid IP address')

    @staticmethod
    def validate_phone_number(v: str) -> str:
        """电话号码验证"""
        # 国际格式电话号码
        pattern = r'^\+?[1-9]\d{1,14}$'
        if not re.match(pattern, v):
            raise ValueError('Invalid phone number format')
        return v

    @staticmethod
    def validate_credit_card(v: str) -> str:
        """信用卡号验证（Luhn算法）"""
        # 移除空格和连字符
        v = re.sub(r'[\s-]', '', v)

        # 检查是否全为数字
        if not v.isdigit():
            raise ValueError('Credit card must contain only digits')

        # 检查长度
        if len(v) < 13 or len(v) > 19:
            raise ValueError('Invalid credit card length')

        # Luhn算法验证
        def luhn_check(card_num):
            def digits_of(n):
                return [int(d) for d in str(n)]

            digits = digits_of(card_num)
            odd_digits = digits[-1::-2]
            even_digits = digits[-2::-2]
            checksum = sum(odd_digits)
            for d in even_digits:
                checksum += sum(digits_of(d*2))
            return checksum % 10 == 0

        if not luhn_check(v):
            raise ValueError('Invalid credit card number')

        return v

    @staticmethod
    def validate_url(v: str) -> str:
        """URL验证"""
        url_pattern = re.compile(
            r'^https?://'  # http:// or https://
            r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'  # domain...
            r'localhost|'  # localhost...
            r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # ...or ip
            r'(?::\d+)?'  # optional port
            r'(?:/?|[/?]\S+)$', re.IGNORECASE)

        if not url_pattern.match(v):
            raise ValueError('Invalid URL format')

        # 禁止内网地址
        from urllib.parse import urlparse
        parsed = urlparse(v)

        if parsed.hostname:
            try:
                ip = ipaddress.ip_address(parsed.hostname)
                if ip.is_private or ip.is_loopback:
                    raise ValueError('Private IP addresses not allowed')
            except ValueError:
                pass  # 不是IP地址，继续

        return v

    @staticmethod
    def validate_json_schema(v: dict, schema: dict) -> dict:
        """JSON Schema验证"""
        import jsonschema

        try:
            jsonschema.validate(v, schema)
            return v
        except jsonschema.ValidationError as e:
            raise ValueError(f'JSON validation error: {e.message}')

# 使用自定义验证器的模型
class SecureAPIRequest(SecureBaseModel):
    """安全的API请求模型"""
    client_ip: str
    callback_url: Optional[str] = None
    metadata: Optional[dict] = None

    @validator('client_ip')
    def validate_client_ip(cls, v):
        return AdvancedValidators.validate_ip_address(v)

    @validator('callback_url')
    def validate_callback_url(cls, v):
        if v:
            return AdvancedValidators.validate_url(v)
        return v

    @validator('metadata')
    def validate_metadata(cls, v):
        if v:
            # 限制元数据大小
            import json
            if len(json.dumps(v)) > 1024:  # 1KB限制
                raise ValueError('Metadata too large')

            # 验证元数据结构
            schema = {
                "type": "object",
                "properties": {
                    "version": {"type": "string"},
                    "source": {"type": "string"},
                    "tags": {"type": "array", "items": {"type": "string"}}
                },
                "additionalProperties": False
            }

            return AdvancedValidators.validate_json_schema(v, schema)

        return v

# 防止序列化攻击的模型
class SecureSerializationModel(SecureBaseModel):
    """防止序列化攻击的模型"""

    @root_validator(pre=True)
    def prevent_deserialization_attacks(cls, values):
        """防止反序列化攻击"""
        dangerous_keys = [
            '__class__', '__module__', '__dict__', '__getattribute__',
            '__subclasshook__', '__reduce__', '__reduce_ex__'
        ]

        def check_dict(obj, path=""):
            if isinstance(obj, dict):
                for key, value in obj.items():
                    if key in dangerous_keys:
                        raise ValueError(f'Dangerous key "{key}" found at {path}')
                    check_dict(value, f"{path}.{key}")
            elif isinstance(obj, list):
                for i, item in enumerate(obj):
                    check_dict(item, f"{path}[{i}]")

        check_dict(values)
        return values

    @validator('*', pre=True)
    def prevent_prototype_pollution(cls, v):
        """防止原型污染"""
        if isinstance(v, dict):
            dangerous_keys = ['__proto__', 'constructor', 'prototype']
            for key in dangerous_keys:
                if key in v:
                    raise ValueError(f'Dangerous key "{key}" detected')

        return v</code></pre>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 异步安全加固 -->
      <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-white/50 mb-8">
        <h2 class="text-3xl font-bold text-purple-800 mb-6 flex items-center">
          <svg class="w-8 h-8 mr-3 text-purple-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
          </svg>
          异步安全加固
        </h2>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-bold text-purple-800 mb-4">安全的异步编程</h3>
            <div class="space-y-4">
              <div class="bg-purple-50 p-4 rounded-lg border border-purple-200">
                <h4 class="font-bold text-purple-700 mb-2">竞态条件防护</h4>
                <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code># app/utils/async_security.py - 异步安全工具
import asyncio
from contextlib import asynccontextmanager
from typing import Dict, Any
import time
import threading

class AsyncSecurityManager:
    """异步安全管理器"""

    def __init__(self):
        self._locks: Dict[str, asyncio.Lock] = {}
        self._semaphores: Dict[str, asyncio.Semaphore] = {}
        self._rate_limiters: Dict[str, Dict] = {}

    async def get_lock(self, key: str) -> asyncio.Lock:
        """获取命名锁"""
        if key not in self._locks:
            self._locks[key] = asyncio.Lock()
        return self._locks[key]

    async def get_semaphore(self, key: str, limit: int = 10) -> asyncio.Semaphore:
        """获取命名信号量"""
        if key not in self._semaphores:
            self._semaphores[key] = asyncio.Semaphore(limit)
        return self._semaphores[key]

    @asynccontextmanager
    async def rate_limit(self, key: str, max_calls: int = 10, period: int = 60):
        """异步限流上下文管理器"""
        current_time = time.time()

        if key not in self._rate_limiters:
            self._rate_limiters[key] = {"calls": [], "max_calls": max_calls, "period": period}

        limiter = self._rate_limiters[key]

        # 清理过期记录
        limiter["calls"] = [call_time for call_time in limiter["calls"]
                           if current_time - call_time < limiter["period"]]

        # 检查限流
        if len(limiter["calls"]) >= limiter["max_calls"]:
            raise HTTPException(status_code=429, detail="Rate limit exceeded")

        # 记录调用
        limiter["calls"].append(current_time)

        try:
            yield
        finally:
            pass

# 安全的异步操作示例
security_manager = AsyncSecurityManager()

@app.post("/transfer")
async def secure_transfer(from_user: int, to_user: int, amount: float):
    """安全的转账操作"""

    # 使用锁防止竞态条件
    lock_key = f"transfer_{min(from_user, to_user)}_{max(from_user, to_user)}"
    async with await security_manager.get_lock(lock_key):

        # 限流保护
        async with security_manager.rate_limit(f"user_{from_user}", max_calls=5, period=60):

            # 获取用户余额
            from_balance = await get_user_balance(from_user)

            if from_balance < amount:
                raise HTTPException(status_code=400, detail="Insufficient funds")

            # 原子性操作
            async with get_async_db() as db:
                try:
                    # 开始事务
                    await db.begin()

                    # 扣除发送方余额
                    await update_user_balance(db, from_user, from_balance - amount)

                    # 增加接收方余额
                    to_balance = await get_user_balance_for_update(db, to_user)
                    await update_user_balance(db, to_user, to_balance + amount)

                    # 记录交易
                    await create_transaction_record(db, from_user, to_user, amount)

                    # 提交事务
                    await db.commit()

                    return {"status": "success", "transaction_id": "tx_123"}

                except Exception as e:
                    # 回滚事务
                    await db.rollback()
                    raise HTTPException(status_code=500, detail="Transfer failed")

@app.get("/process-file")
async def secure_file_processing(filename: str):
    """安全的文件处理"""

    # 限制并发文件处理数量
    semaphore = await security_manager.get_semaphore("file_processing", limit=5)

    async with semaphore:
        # 验证文件名
        if not re.match(r'^[a-zA-Z0-9._-]+$', filename):
            raise HTTPException(status_code=400, detail="Invalid filename")

        file_path = Path("uploads") / filename

        # 确保文件在安全目录内
        try:
            file_path.resolve().relative_to(Path("uploads").resolve())
        except ValueError:
            raise HTTPException(status_code=400, detail="Access denied")

        try:
            # 使用异步文件操作
            async with aiofiles.open(file_path, 'r') as f:
                content = await f.read()

            # 处理文件内容
            result = await process_file_content(content)

            return {"result": result}

        except FileNotFoundError:
            raise HTTPException(status_code=404, detail="File not found")
        except Exception as e:
            raise HTTPException(status_code=500, detail="Processing failed")

# 安全的上下文管理
from contextvars import ContextVar

# 使用ContextVar而不是全局变量
current_user_context: ContextVar[Optional[dict]] = ContextVar('current_user', default=None)
request_id_context: ContextVar[Optional[str]] = ContextVar('request_id', default=None)

@app.middleware("http")
async def secure_context_middleware(request: Request, call_next):
    """安全的上下文中间件"""

    # 生成请求ID
    request_id = secrets.token_urlsafe(16)
    request_id_context.set(request_id)

    # 清理上下文
    current_user_context.set(None)

    try:
        response = await call_next(request)
        response.headers["X-Request-ID"] = request_id
        return response
    finally:
        # 清理上下文
        current_user_context.set(None)
        request_id_context.set(None)</code></pre>
              </div>
            </div>
          </div>

          <div>
            <h3 class="text-xl font-bold text-purple-800 mb-4">资源管理和监控</h3>
            <div class="space-y-4">
              <div class="bg-blue-50 p-4 rounded-lg border border-blue-200">
                <h4 class="font-bold text-blue-700 mb-2">资源监控和保护</h4>
                <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code># app/monitoring/async_monitor.py - 异步监控
import asyncio
import psutil
import time
from typing import Dict, List
import logging

class AsyncResourceMonitor:
    """异步资源监控器"""

    def __init__(self):
        self.active_tasks: Dict[str, asyncio.Task] = {}
        self.resource_usage: Dict[str, List] = {
            "cpu": [],
            "memory": [],
            "connections": []
        }
        self.max_concurrent_tasks = 100
        self.max_memory_usage = 80  # 百分比

    async def monitor_resources(self):
        """监控系统资源"""
        while True:
            try:
                # CPU使用率
                cpu_percent = psutil.cpu_percent(interval=1)
                self.resource_usage["cpu"].append({
                    "timestamp": time.time(),
                    "value": cpu_percent
                })

                # 内存使用率
                memory = psutil.virtual_memory()
                self.resource_usage["memory"].append({
                    "timestamp": time.time(),
                    "value": memory.percent
                })

                # 网络连接数
                connections = len(psutil.net_connections())
                self.resource_usage["connections"].append({
                    "timestamp": time.time(),
                    "value": connections
                })

                # 清理旧数据（保留最近1小时）
                current_time = time.time()
                for metric in self.resource_usage:
                    self.resource_usage[metric] = [
                        item for item in self.resource_usage[metric]
                        if current_time - item["timestamp"] < 3600
                    ]

                # 检查资源使用情况
                await self._check_resource_limits()

                await asyncio.sleep(10)  # 每10秒检查一次

            except Exception as e:
                logging.error(f"Resource monitoring error: {e}")
                await asyncio.sleep(60)  # 出错时等待更长时间

    async def _check_resource_limits(self):
        """检查资源限制"""
        # 检查内存使用
        if self.resource_usage["memory"]:
            latest_memory = self.resource_usage["memory"][-1]["value"]
            if latest_memory > self.max_memory_usage:
                logging.warning(f"High memory usage: {latest_memory}%")
                await self._handle_high_memory_usage()

        # 检查并发任务数
        active_task_count = len([task for task in self.active_tasks.values() if not task.done()])
        if active_task_count > self.max_concurrent_tasks:
            logging.warning(f"Too many concurrent tasks: {active_task_count}")
            await self._handle_too_many_tasks()

    async def _handle_high_memory_usage(self):
        """处理高内存使用"""
        # 取消一些非关键任务
        non_critical_tasks = [
            task for name, task in self.active_tasks.items()
            if name.startswith("background_") and not task.done()
        ]

        for task in non_critical_tasks[:5]:  # 取消最多5个任务
            task.cancel()
            logging.info("Cancelled background task due to high memory usage")

    async def _handle_too_many_tasks(self):
        """处理过多并发任务"""
        # 实施任务队列或拒绝新任务
        logging.warning("Implementing task throttling due to high concurrency")

    def register_task(self, name: str, task: asyncio.Task):
        """注册任务"""
        self.active_tasks[name] = task

        # 清理已完成的任务
        self.active_tasks = {
            name: task for name, task in self.active_tasks.items()
            if not task.done()
        }

    def get_resource_stats(self) -> Dict:
        """获取资源统计"""
        stats = {}

        for metric, data in self.resource_usage.items():
            if data:
                values = [item["value"] for item in data]
                stats[metric] = {
                    "current": values[-1] if values else 0,
                    "average": sum(values) / len(values),
                    "max": max(values),
                    "min": min(values)
                }

        stats["active_tasks"] = len([task for task in self.active_tasks.values() if not task.done()])

        return stats

# 全局监控器实例
resource_monitor = AsyncResourceMonitor()

# 启动监控
@app.on_event("startup")
async def start_monitoring():
    """启动资源监控"""
    monitor_task = asyncio.create_task(resource_monitor.monitor_resources())
    resource_monitor.register_task("resource_monitor", monitor_task)

# 安全的任务创建装饰器
def secure_background_task(name: str = None):
    """安全的后台任务装饰器"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            task_name = name or f"{func.__name__}_{int(time.time())}"

            try:
                # 检查资源使用情况
                stats = resource_monitor.get_resource_stats()
                if stats.get("active_tasks", 0) > resource_monitor.max_concurrent_tasks:
                    raise HTTPException(status_code=503, detail="Server too busy")

                # 创建任务
                task = asyncio.create_task(func(*args, **kwargs))
                resource_monitor.register_task(task_name, task)

                return await task

            except asyncio.CancelledError:
                logging.info(f"Task {task_name} was cancelled")
                raise
            except Exception as e:
                logging.error(f"Task {task_name} failed: {e}")
                raise

        return wrapper
    return decorator

# 使用示例
@secure_background_task("data_processing")
async def process_large_dataset(data: List[Dict]):
    """处理大型数据集"""
    # 分批处理以避免内存问题
    batch_size = 100
    results = []

    for i in range(0, len(data), batch_size):
        batch = data[i:i + batch_size]

        # 检查是否被取消
        if asyncio.current_task().cancelled():
            break

        # 处理批次
        batch_result = await process_batch(batch)
        results.extend(batch_result)

        # 让出控制权
        await asyncio.sleep(0.1)

    return results</code></pre>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FastAPIHardening'
}
</script>

<style scoped>
@keyframes blob {
  0% { transform: translate(0px, 0px) scale(1); }
  33% { transform: translate(30px, -50px) scale(1.1); }
  66% { transform: translate(-20px, 20px) scale(0.9); }
  100% { transform: translate(0px, 0px) scale(1); }
}

.animate-blob {
  animation: blob 7s infinite;
}

.animation-delay-2000 {
  animation-delay: 2s;
}
</style>
