<template>
  <div class="min-h-screen bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50 flex flex-col relative">
    <!-- 背景装饰 -->
    <div class="absolute inset-0 overflow-hidden pointer-events-none">
      <div class="absolute -top-40 -right-40 w-80 h-80 bg-green-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob"></div>
      <div class="absolute -bottom-40 -left-40 w-80 h-80 bg-emerald-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000"></div>
    </div>

    <div class="max-w-7xl mx-auto py-12 px-4 sm:px-6 md:px-8 w-full relative z-10">
      <!-- 返回按钮 -->
      <div class="mb-8">
        <router-link to="/web/framework/python/fastapi" class="inline-flex items-center text-green-600 hover:text-green-700 transition-colors duration-200">
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
          </svg>
          返回 FastAPI 框架
        </router-link>
      </div>

      <!-- 页面头部 -->
      <div class="text-center mb-12">
        <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-600 rounded-full mb-6 shadow-lg">
          <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C20.832 18.477 19.246 18 17.5 18c-1.746 0-3.332.477-4.5 1.253"/>
          </svg>
        </div>
        <h1 class="text-4xl font-extrabold text-transparent bg-clip-text bg-gradient-to-r from-green-600 to-emerald-600 mb-4">
          FastAPI 基础知识
        </h1>
        <p class="text-xl text-gray-700 max-w-4xl mx-auto leading-relaxed">
          ⚡ 全面掌握FastAPI现代异步框架的核心概念、架构设计和基础用法
        </p>
      </div>

      <!-- FastAPI核心架构 -->
      <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-white/50 mb-8">
        <h2 class="text-3xl font-bold text-green-800 mb-6 flex items-center">
          <svg class="w-8 h-8 mr-3 text-green-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M13 10V3L4 14h7v7l9-11h-7z"/>
          </svg>
          FastAPI 核心架构
        </h2>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-bold text-green-800 mb-4">现代框架设计</h3>
            <div class="space-y-4">
              <div class="bg-green-50 p-4 rounded-lg border border-green-200">
                <h4 class="font-bold text-green-700 mb-2">FastAPI核心特性</h4>
                <p class="text-green-600 text-sm mb-3">
                  FastAPI是基于Python类型提示的现代高性能Web框架，
                  支持异步编程，自动生成API文档，具有出色的开发体验。
                </p>
                <div class="space-y-2 text-sm">
                  <div class="bg-white p-2 rounded border">
                    <strong>高性能：</strong>基于Starlette和Pydantic，性能媲美NodeJS和Go
                  </div>
                  <div class="bg-white p-2 rounded border">
                    <strong>类型提示：</strong>完整的Python类型提示支持
                  </div>
                  <div class="bg-white p-2 rounded border">
                    <strong>自动文档：</strong>自动生成OpenAPI和JSON Schema
                  </div>
                  <div class="bg-white p-2 rounded border">
                    <strong>异步支持：</strong>原生async/await支持
                  </div>
                  <div class="bg-white p-2 rounded border">
                    <strong>数据验证：</strong>基于Pydantic的自动数据验证
                  </div>
                </div>
              </div>

              <div class="bg-emerald-50 p-4 rounded-lg border border-emerald-200">
                <h4 class="font-bold text-emerald-700 mb-2">核心组件</h4>
                <div class="space-y-2 text-sm">
                  <div class="bg-white p-2 rounded border">
                    <strong>FastAPI应用：</strong>ASGI应用实例
                  </div>
                  <div class="bg-white p-2 rounded border">
                    <strong>路径操作：</strong>HTTP方法和路径的处理函数
                  </div>
                  <div class="bg-white p-2 rounded border">
                    <strong>依赖注入：</strong>强大的依赖注入系统
                  </div>
                  <div class="bg-white p-2 rounded border">
                    <strong>Pydantic模型：</strong>数据验证和序列化
                  </div>
                  <div class="bg-white p-2 rounded border">
                    <strong>中间件：</strong>请求/响应处理中间件
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div>
            <h3 class="text-xl font-bold text-green-800 mb-4">项目结构</h3>
            <div class="bg-gray-50 p-4 rounded-lg border">
              <pre class="text-sm text-gray-700 overflow-x-auto"><code>fastapi-project/
├── main.py                 # 应用入口文件
├── requirements.txt        # 依赖包列表
├── .env                    # 环境变量
├── Dockerfile             # Docker配置
├── docker-compose.yml     # Docker Compose配置
├── app/                    # 应用包
│   ├── __init__.py
│   ├── main.py            # FastAPI应用实例
│   ├── config.py          # 配置文件
│   ├── dependencies.py    # 依赖注入
│   ├── database.py        # 数据库配置
│   ├── models/            # 数据模型
│   │   ├── __init__.py
│   │   ├── user.py
│   │   └── item.py
│   ├── schemas/           # Pydantic模式
│   │   ├── __init__.py
│   │   ├── user.py
│   │   └── item.py
│   ├── crud/              # CRUD操作
│   │   ├── __init__.py
│   │   ├── user.py
│   │   └── item.py
│   ├── api/               # API路由
│   │   ├── __init__.py
│   │   ├── api_v1/
│   │   │   ├── __init__.py
│   │   │   ├── api.py
│   │   │   └── endpoints/
│   │   │       ├── __init__.py
│   │   │       ├── users.py
│   │   │       ├── items.py
│   │   │       └── auth.py
│   │   └── deps.py        # API依赖
│   ├── core/              # 核心功能
│   │   ├── __init__.py
│   │   ├── config.py      # 核心配置
│   │   ├── security.py    # 安全功能
│   │   └── auth.py        # 认证功能
│   ├── middleware/        # 中间件
│   │   ├── __init__.py
│   │   └── cors.py
│   └── utils/             # 工具函数
│       ├── __init__.py
│       └── helpers.py
├── tests/                 # 测试文件
│   ├── __init__.py
│   ├── conftest.py
│   ├── test_main.py
│   └── api/
│       └── test_users.py
├── alembic/               # 数据库迁移
│   ├── versions/
│   ├── env.py
│   └── alembic.ini
└── docs/                  # 文档
    └── api.md</code></pre>
            </div>
          </div>
        </div>
      </div>

      <!-- FastAPI应用创建 -->
      <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-white/50 mb-8">
        <h2 class="text-3xl font-bold text-emerald-800 mb-6 flex items-center">
          <svg class="w-8 h-8 mr-3 text-emerald-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
            <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
          </svg>
          FastAPI 应用创建
        </h2>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-bold text-emerald-800 mb-4">基础应用</h3>
            <div class="space-y-4">
              <div class="bg-green-50 p-3 rounded border border-green-200">
                <strong class="text-green-700">简单FastAPI应用：</strong>
                <pre class="bg-white p-2 rounded mt-2 text-xs"><code># main.py
from fastapi import FastAPI, HTTPException, Depends, status
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from pydantic import BaseModel
from typing import Optional, List
import uvicorn

# 创建FastAPI应用实例
app = FastAPI(
    title="My FastAPI App",
    description="A simple FastAPI application",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Pydantic模型
class Item(BaseModel):
    id: Optional[int] = None
    name: str
    description: Optional[str] = None
    price: float
    tax: Optional[float] = None

class User(BaseModel):
    id: Optional[int] = None
    username: str
    email: str
    full_name: Optional[str] = None

# 模拟数据库
fake_items_db = []
fake_users_db = []

# 基础路由
@app.get("/")
async def root():
    return {"message": "Hello FastAPI!"}

@app.get("/health")
async def health_check():
    return {"status": "healthy"}

# 路径参数
@app.get("/items/{item_id}")
async def read_item(item_id: int, q: Optional[str] = None):
    if item_id not in range(len(fake_items_db)):
        raise HTTPException(status_code=404, detail="Item not found")
    
    item = fake_items_db[item_id]
    if q:
        item["query"] = q
    return item

# 请求体
@app.post("/items/", response_model=Item)
async def create_item(item: Item):
    item.id = len(fake_items_db)
    fake_items_db.append(item.dict())
    return item

# 查询参数
@app.get("/items/", response_model=List[Item])
async def read_items(skip: int = 0, limit: int = 10):
    return fake_items_db[skip : skip + limit]

# 异步路由
@app.get("/async-items/{item_id}")
async def read_async_item(item_id: int):
    # 模拟异步操作
    import asyncio
    await asyncio.sleep(0.1)
    
    if item_id >= len(fake_items_db):
        raise HTTPException(status_code=404, detail="Item not found")
    
    return fake_items_db[item_id]

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)</code></pre>
              </div>
            </div>
          </div>

          <div>
            <h3 class="text-xl font-bold text-emerald-800 mb-4">应用工厂模式</h3>
            <div class="space-y-4">
              <div class="bg-blue-50 p-3 rounded border border-blue-200">
                <strong class="text-blue-700">应用工厂：</strong>
                <pre class="bg-white p-2 rounded mt-2 text-xs"><code># app/main.py
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from app.api.api_v1.api import api_router
from app.core.config import settings

def create_application() -> FastAPI:
    """创建FastAPI应用实例"""
    
    application = FastAPI(
        title=settings.PROJECT_NAME,
        description=settings.PROJECT_DESCRIPTION,
        version=settings.VERSION,
        openapi_url=f"{settings.API_V1_STR}/openapi.json",
        docs_url="/docs" if settings.DOCS_ENABLED else None,
        redoc_url="/redoc" if settings.DOCS_ENABLED else None,
    )
    
    # 配置CORS
    if settings.BACKEND_CORS_ORIGINS:
        application.add_middleware(
            CORSMiddleware,
            allow_origins=[str(origin) for origin in settings.BACKEND_CORS_ORIGINS],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
    
    # 包含路由
    application.include_router(api_router, prefix=settings.API_V1_STR)
    
    # 添加启动事件
    @application.on_event("startup")
    async def startup_event():
        # 初始化数据库连接
        pass
    
    @application.on_event("shutdown")
    async def shutdown_event():
        # 清理资源
        pass
    
    return application

app = create_application()

# app/core/config.py
from pydantic import BaseSettings, AnyHttpUrl
from typing import List, Optional

class Settings(BaseSettings):
    PROJECT_NAME: str = "FastAPI Project"
    PROJECT_DESCRIPTION: str = "A FastAPI project"
    VERSION: str = "1.0.0"
    API_V1_STR: str = "/api/v1"
    
    # CORS
    BACKEND_CORS_ORIGINS: List[AnyHttpUrl] = []
    
    # 数据库
    DATABASE_URL: Optional[str] = None
    
    # 安全
    SECRET_KEY: str
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # 文档
    DOCS_ENABLED: bool = True
    
    class Config:
        env_file = ".env"

settings = Settings()</code></pre>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 依赖注入系统 -->
      <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-white/50 mb-8">
        <h2 class="text-3xl font-bold text-teal-800 mb-6 flex items-center">
          <svg class="w-8 h-8 mr-3 text-teal-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"/>
          </svg>
          FastAPI 依赖注入系统
        </h2>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-bold text-teal-800 mb-4">基础依赖注入</h3>
            <div class="space-y-4">
              <div class="bg-teal-50 p-3 rounded border border-teal-200">
                <strong class="text-teal-700">简单依赖：</strong>
                <pre class="bg-white p-2 rounded mt-2 text-xs"><code># 基础依赖注入
from fastapi import Depends, HTTPException, status
from typing import Optional

# 简单依赖函数
async def common_parameters(q: Optional[str] = None, skip: int = 0, limit: int = 100):
    return {"q": q, "skip": skip, "limit": limit}

# 使用依赖
@app.get("/items/")
async def read_items(commons: dict = Depends(common_parameters)):
    return commons

# 数据库依赖
from sqlalchemy.orm import Session
from app.database import SessionLocal

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

@app.get("/users/{user_id}")
async def read_user(user_id: int, db: Session = Depends(get_db)):
    user = db.query(User).filter(User.id == user_id).first()
    if user is None:
        raise HTTPException(status_code=404, detail="User not found")
    return user

# 认证依赖
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
import jwt

security = HTTPBearer()

async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    token = credentials.credentials
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=["HS256"])
        username: str = payload.get("sub")
        if username is None:
            raise HTTPException(status_code=401, detail="Invalid token")
    except jwt.PyJWTError:
        raise HTTPException(status_code=401, detail="Invalid token")

    # 从数据库获取用户
    user = get_user_by_username(username)
    if user is None:
        raise HTTPException(status_code=401, detail="User not found")

    return user

@app.get("/users/me")
async def read_users_me(current_user: User = Depends(get_current_user)):
    return current_user</code></pre>
              </div>
            </div>
          </div>

          <div>
            <h3 class="text-xl font-bold text-teal-800 mb-4">高级依赖注入</h3>
            <div class="space-y-4">
              <div class="bg-blue-50 p-3 rounded border border-blue-200">
                <strong class="text-blue-700">类依赖和子依赖：</strong>
                <pre class="bg-white p-2 rounded mt-2 text-xs"><code># 类依赖
class CommonQueryParams:
    def __init__(self, q: Optional[str] = None, skip: int = 0, limit: int = 100):
        self.q = q
        self.skip = skip
        self.limit = limit

@app.get("/items/")
async def read_items(commons: CommonQueryParams = Depends(CommonQueryParams)):
    return commons

# 子依赖
async def verify_token(token: str = Depends(oauth2_scheme)):
    # 验证token
    return decode_token(token)

async def get_current_user(token_data: dict = Depends(verify_token)):
    # 根据token数据获取用户
    return get_user(token_data["sub"])

async def get_current_active_user(current_user: User = Depends(get_current_user)):
    if not current_user.is_active:
        raise HTTPException(status_code=400, detail="Inactive user")
    return current_user

@app.get("/users/me/items/")
async def read_own_items(current_user: User = Depends(get_current_active_user)):
    return [{"item_id": "Foo", "owner": current_user.username}]

# 依赖缓存
from functools import lru_cache

@lru_cache()
def get_settings():
    return Settings()

@app.get("/info")
async def info(settings: Settings = Depends(get_settings)):
    return {
        "app_name": settings.app_name,
        "admin_email": settings.admin_email,
        "items_per_user": settings.items_per_user,
    }

# 全局依赖
app = FastAPI(dependencies=[Depends(verify_api_key)])

# 路由级依赖
router = APIRouter(
    prefix="/items",
    tags=["items"],
    dependencies=[Depends(get_token_header)],
    responses={404: {"description": "Not found"}},
)</code></pre>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Pydantic数据验证 -->
      <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-white/50 mb-8">
        <h2 class="text-3xl font-bold text-cyan-800 mb-6 flex items-center">
          <svg class="w-8 h-8 mr-3 text-cyan-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
          </svg>
          Pydantic 数据验证
        </h2>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-bold text-cyan-800 mb-4">基础模型</h3>
            <div class="space-y-4">
              <div class="bg-cyan-50 p-3 rounded border border-cyan-200">
                <strong class="text-cyan-700">Pydantic模型定义：</strong>
                <pre class="bg-white p-2 rounded mt-2 text-xs"><code># schemas/user.py
from pydantic import BaseModel, EmailStr, validator, Field
from typing import Optional, List
from datetime import datetime
from enum import Enum

class UserRole(str, Enum):
    admin = "admin"
    user = "user"
    moderator = "moderator"

class UserBase(BaseModel):
    username: str = Field(..., min_length=3, max_length=50, regex="^[a-zA-Z0-9_]+$")
    email: EmailStr
    full_name: Optional[str] = Field(None, max_length=100)
    role: UserRole = UserRole.user
    is_active: bool = True

class UserCreate(UserBase):
    password: str = Field(..., min_length=8, max_length=100)

    @validator('password')
    def validate_password(cls, v):
        if not any(c.isupper() for c in v):
            raise ValueError('Password must contain at least one uppercase letter')
        if not any(c.islower() for c in v):
            raise ValueError('Password must contain at least one lowercase letter')
        if not any(c.isdigit() for c in v):
            raise ValueError('Password must contain at least one digit')
        return v

class UserUpdate(BaseModel):
    username: Optional[str] = Field(None, min_length=3, max_length=50)
    email: Optional[EmailStr] = None
    full_name: Optional[str] = Field(None, max_length=100)
    role: Optional[UserRole] = None
    is_active: Optional[bool] = None

class UserInDB(UserBase):
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        orm_mode = True

class UserResponse(UserBase):
    id: int
    created_at: datetime

    class Config:
        orm_mode = True

# 嵌套模型
class Address(BaseModel):
    street: str
    city: str
    country: str
    postal_code: str = Field(..., regex="^[0-9]{5,10}$")

class UserWithAddress(UserResponse):
    address: Optional[Address] = None</code></pre>
              </div>
            </div>
          </div>

          <div>
            <h3 class="text-xl font-bold text-cyan-800 mb-4">高级验证</h3>
            <div class="space-y-4">
              <div class="bg-blue-50 p-3 rounded border border-blue-200">
                <strong class="text-blue-700">自定义验证器：</strong>
                <pre class="bg-white p-2 rounded mt-2 text-xs"><code># 自定义验证器
from pydantic import BaseModel, validator, root_validator
import re

class ProductModel(BaseModel):
    name: str
    price: float
    category: str
    tags: List[str] = []

    @validator('name')
    def name_must_not_be_empty(cls, v):
        if not v.strip():
            raise ValueError('Name cannot be empty')
        return v.strip()

    @validator('price')
    def price_must_be_positive(cls, v):
        if v <= 0:
            raise ValueError('Price must be positive')
        return v

    @validator('tags')
    def validate_tags(cls, v):
        if len(v) > 10:
            raise ValueError('Too many tags')
        return [tag.lower().strip() for tag in v]

    @root_validator
    def validate_category_tags(cls, values):
        category = values.get('category')
        tags = values.get('tags', [])

        if category == 'electronics' and 'tech' not in tags:
            raise ValueError('Electronics must have tech tag')

        return values

# 条件验证
class ConditionalModel(BaseModel):
    user_type: str
    admin_code: Optional[str] = None

    @root_validator
    def validate_admin_code(cls, values):
        user_type = values.get('user_type')
        admin_code = values.get('admin_code')

        if user_type == 'admin' and not admin_code:
            raise ValueError('Admin users must provide admin code')

        return values

# 自定义字段类型
from pydantic import BaseModel
from typing import Any

class PhoneNumber(str):
    @classmethod
    def __get_validators__(cls):
        yield cls.validate

    @classmethod
    def validate(cls, v):
        if not isinstance(v, str):
            raise TypeError('string required')

        # 简单的电话号码验证
        phone_regex = re.compile(r'^\+?1?\d{9,15}$')
        if not phone_regex.match(v):
            raise ValueError('Invalid phone number format')

        return cls(v)

class ContactModel(BaseModel):
    name: str
    phone: PhoneNumber
    email: EmailStr</code></pre>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 异步编程 -->
      <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-white/50 mb-8">
        <h2 class="text-3xl font-bold text-purple-800 mb-6 flex items-center">
          <svg class="w-8 h-8 mr-3 text-purple-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
          </svg>
          FastAPI 异步编程
        </h2>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-bold text-purple-800 mb-4">异步路径操作</h3>
            <div class="space-y-4">
              <div class="bg-purple-50 p-3 rounded border border-purple-200">
                <strong class="text-purple-700">异步函数：</strong>
                <pre class="bg-white p-2 rounded mt-2 text-xs"><code># 异步路径操作
import asyncio
import aiohttp
import aiofiles
from fastapi import BackgroundTasks

# 基础异步路由
@app.get("/async-hello")
async def async_hello():
    # 模拟异步操作
    await asyncio.sleep(1)
    return {"message": "Hello from async!"}

# 异步HTTP请求
@app.get("/fetch-data/{url}")
async def fetch_external_data(url: str):
    async with aiohttp.ClientSession() as session:
        async with session.get(url) as response:
            if response.status == 200:
                data = await response.json()
                return {"status": "success", "data": data}
            else:
                return {"status": "error", "code": response.status}

# 异步文件操作
@app.post("/upload-file/")
async def upload_file(file: UploadFile = File(...)):
    file_path = f"uploads/{file.filename}"

    async with aiofiles.open(file_path, 'wb') as f:
        content = await file.read()
        await f.write(content)

    return {"filename": file.filename, "size": len(content)}

@app.get("/read-file/{filename}")
async def read_file(filename: str):
    file_path = f"uploads/{filename}"

    try:
        async with aiofiles.open(file_path, 'r') as f:
            content = await f.read()
        return {"filename": filename, "content": content}
    except FileNotFoundError:
        raise HTTPException(status_code=404, detail="File not found")

# 异步数据库操作
from databases import Database

database = Database("postgresql://user:password@localhost/dbname")

@app.on_event("startup")
async def startup():
    await database.connect()

@app.on_event("shutdown")
async def shutdown():
    await database.disconnect()

@app.get("/users/{user_id}")
async def get_user(user_id: int):
    query = "SELECT * FROM users WHERE id = :user_id"
    user = await database.fetch_one(query=query, values={"user_id": user_id})

    if user is None:
        raise HTTPException(status_code=404, detail="User not found")

    return dict(user)</code></pre>
              </div>
            </div>
          </div>

          <div>
            <h3 class="text-xl font-bold text-purple-800 mb-4">后台任务</h3>
            <div class="space-y-4">
              <div class="bg-blue-50 p-3 rounded border border-blue-200">
                <strong class="text-blue-700">后台任务处理：</strong>
                <pre class="bg-white p-2 rounded mt-2 text-xs"><code># 后台任务
from fastapi import BackgroundTasks
import smtplib
from email.mime.text import MIMEText

def send_email(email: str, message: str):
    """发送邮件的后台任务"""
    # 模拟发送邮件
    print(f"Sending email to {email}: {message}")
    # 实际的邮件发送逻辑
    time.sleep(2)  # 模拟耗时操作

@app.post("/send-notification/")
async def send_notification(
    email: str,
    message: str,
    background_tasks: BackgroundTasks
):
    # 添加后台任务
    background_tasks.add_task(send_email, email, message)
    return {"message": "Notification sent in the background"}

# 复杂后台任务
def process_data(data: dict, user_id: int):
    """数据处理后台任务"""
    # 模拟数据处理
    print(f"Processing data for user {user_id}")

    # 处理逻辑
    result = {"processed": True, "user_id": user_id}

    # 保存结果到数据库
    save_processing_result(user_id, result)

@app.post("/process/")
async def start_processing(
    data: dict,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user)
):
    background_tasks.add_task(process_data, data, current_user.id)
    return {"message": "Processing started", "user_id": current_user.id}

# 使用Celery进行更复杂的后台任务
from celery import Celery

celery_app = Celery("fastapi_app")

@celery_app.task
def heavy_computation(data: dict):
    """重型计算任务"""
    # 执行复杂计算
    import time
    time.sleep(10)  # 模拟长时间运行的任务
    return {"result": "computation completed", "data": data}

@app.post("/heavy-task/")
async def start_heavy_task(data: dict):
    task = heavy_computation.delay(data)
    return {"task_id": task.id, "status": "started"}

@app.get("/task-status/{task_id}")
async def get_task_status(task_id: str):
    task = heavy_computation.AsyncResult(task_id)
    return {"task_id": task_id, "status": task.status, "result": task.result}</code></pre>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FastAPIBasics'
}
</script>

<style scoped>
@keyframes blob {
  0% { transform: translate(0px, 0px) scale(1); }
  33% { transform: translate(30px, -50px) scale(1.1); }
  66% { transform: translate(-20px, 20px) scale(0.9); }
  100% { transform: translate(0px, 0px) scale(1); }
}

.animate-blob {
  animation: blob 7s infinite;
}

.animation-delay-2000 {
  animation-delay: 2s;
}
</style>
