<template>
  <div class="min-h-screen bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50 flex flex-col relative">
    <!-- 背景装饰 -->
    <div class="absolute inset-0 overflow-hidden pointer-events-none">
      <div class="absolute -top-40 -right-40 w-80 h-80 bg-green-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob"></div>
      <div class="absolute -bottom-40 -left-40 w-80 h-80 bg-emerald-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000"></div>
    </div>

    <div class="max-w-7xl mx-auto py-12 px-4 sm:px-6 md:px-8 w-full relative z-10">
      <!-- 返回按钮 -->
      <div class="mb-8">
        <router-link to="/web/framework/python/fastapi" class="inline-flex items-center text-green-600 hover:text-green-700 transition-colors duration-200">
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
          </svg>
          返回 FastAPI 框架
        </router-link>
      </div>

      <!-- 页面头部 -->
      <div class="text-center mb-12">
        <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-600 rounded-full mb-6 shadow-lg">
          <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
            <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
          </svg>
        </div>
        <h1 class="text-4xl font-extrabold text-transparent bg-clip-text bg-gradient-to-r from-green-600 to-emerald-600 mb-4">
          FastAPI 配置文件详解
        </h1>
        <p class="text-xl text-gray-700 max-w-4xl mx-auto leading-relaxed">
          ⚙️ 深入理解FastAPI配置文件的结构、参数含义和最佳实践
        </p>
      </div>

      <!-- 配置文件概览 -->
      <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-white/50 mb-8">
        <h2 class="text-3xl font-bold text-green-800 mb-6 flex items-center">
          <svg class="w-8 h-8 mr-3 text-green-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
          </svg>
          配置文件概览
        </h2>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-bold text-green-800 mb-4">配置文件结构</h3>
            <div class="bg-gray-50 p-4 rounded-lg border">
              <pre class="text-sm text-gray-700 overflow-x-auto"><code>fastapi-project/
├── app/
│   ├── core/
│   │   ├── __init__.py
│   │   ├── config.py         # 主配置文件
│   │   ├── settings.py       # 设置类
│   │   └── security.py       # 安全配置
│   ├── config/               # 配置模块
│   │   ├── __init__.py
│   │   ├── development.py    # 开发环境配置
│   │   ├── production.py     # 生产环境配置
│   │   ├── testing.py        # 测试环境配置
│   │   └── base.py           # 基础配置
│   └── main.py               # 应用入口
├── .env                      # 环境变量文件
├── .env.example              # 环境变量示例
├── .env.local                # 本地环境变量
├── .env.production           # 生产环境变量
├── .env.testing              # 测试环境变量
├── alembic.ini               # 数据库迁移配置
├── pyproject.toml            # 项目配置
├── requirements.txt          # 依赖文件
├── requirements/             # 分环境依赖
│   ├── base.txt              # 基础依赖
│   ├── development.txt       # 开发依赖
│   ├── production.txt        # 生产依赖
│   └── testing.txt           # 测试依赖
├── docker-compose.yml        # Docker配置
├── Dockerfile                # Docker镜像配置
└── gunicorn.conf.py          # Gunicorn配置</code></pre>
            </div>
          </div>

          <div>
            <h3 class="text-xl font-bold text-green-800 mb-4">配置加载机制</h3>
            <div class="space-y-4">
              <div class="bg-green-50 p-3 rounded border border-green-200">
                <h4 class="font-bold text-green-700 mb-2">Pydantic Settings</h4>
                <div class="space-y-2 text-sm">
                  <div class="bg-white p-2 rounded border">
                    <strong>1. 环境变量：</strong>优先级最高
                  </div>
                  <div class="bg-white p-2 rounded border">
                    <strong>2. .env文件：</strong>自动加载环境变量
                  </div>
                  <div class="bg-white p-2 rounded border">
                    <strong>3. 默认值：</strong>代码中定义的默认值
                  </div>
                  <div class="bg-white p-2 rounded border">
                    <strong>4. 类型验证：</strong>自动类型转换和验证
                  </div>
                </div>
              </div>

              <div class="bg-emerald-50 p-3 rounded border border-emerald-200">
                <h4 class="font-bold text-emerald-700 mb-2">配置特点</h4>
                <pre class="bg-white p-2 rounded mt-2 text-xs"><code># FastAPI配置特点
from pydantic import BaseSettings

class Settings(BaseSettings):
    # 自动类型验证
    debug: bool = False
    port: int = 8000
    
    # 环境变量映射
    database_url: str
    redis_url: str = "redis://localhost:6379"
    
    # 嵌套配置
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        
# 使用方式
settings = Settings()
print(settings.debug)  # 自动从环境变量或.env文件读取</code></pre>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 核心配置文件 -->
      <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-white/50 mb-8">
        <h2 class="text-3xl font-bold text-emerald-800 mb-6 flex items-center">
          <svg class="w-8 h-8 mr-3 text-emerald-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"/>
          </svg>
          核心配置文件 - config.py
        </h2>

        <div class="space-y-6">
          <div class="bg-green-50 p-4 rounded-lg border border-green-200">
            <h4 class="font-bold text-green-700 mb-3">基础配置类</h4>
            <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code># app/core/config.py
from pydantic import BaseSettings, AnyHttpUrl, EmailStr, validator
from typing import List, Optional, Union, Dict, Any
import secrets
from functools import lru_cache

class Settings(BaseSettings):
    """FastAPI应用配置"""
    
    # 应用基础配置
    PROJECT_NAME: str = "FastAPI Project"
    PROJECT_DESCRIPTION: str = "A FastAPI application"
    VERSION: str = "1.0.0"
    API_V1_STR: str = "/api/v1"
    
    # 服务器配置
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    DEBUG: bool = False
    RELOAD: bool = False
    
    # 安全配置
    SECRET_KEY: str = secrets.token_urlsafe(32)
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 7  # 7 days
    ALGORITHM: str = "HS256"
    
    # 数据库配置
    DATABASE_URL: Optional[str] = None
    ASYNC_DATABASE_URL: Optional[str] = None
    DATABASE_ECHO: bool = False
    DATABASE_POOL_SIZE: int = 5
    DATABASE_MAX_OVERFLOW: int = 10
    
    # Redis配置
    REDIS_URL: str = "redis://localhost:6379/0"
    REDIS_CACHE_TTL: int = 300  # 5 minutes
    
    # CORS配置
    BACKEND_CORS_ORIGINS: List[AnyHttpUrl] = []
    
    @validator("BACKEND_CORS_ORIGINS", pre=True)
    def assemble_cors_origins(cls, v: Union[str, List[str]]) -> Union[List[str], str]:
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)
    
    # 邮件配置
    SMTP_TLS: bool = True
    SMTP_PORT: Optional[int] = None
    SMTP_HOST: Optional[str] = None
    SMTP_USER: Optional[str] = None
    SMTP_PASSWORD: Optional[str] = None
    EMAILS_FROM_EMAIL: Optional[EmailStr] = None
    EMAILS_FROM_NAME: Optional[str] = None
    
    # 文件上传配置
    UPLOAD_DIR: str = "uploads"
    MAX_UPLOAD_SIZE: int = 10 * 1024 * 1024  # 10MB
    ALLOWED_EXTENSIONS: List[str] = ["jpg", "jpeg", "png", "gif", "pdf", "txt"]
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    LOG_FILE: Optional[str] = None
    
    # 第三方服务配置
    SENTRY_DSN: Optional[str] = None
    
    # 缓存配置
    CACHE_BACKEND: str = "redis"
    CACHE_TTL: int = 300
    
    # 限流配置
    RATE_LIMIT_ENABLED: bool = True
    RATE_LIMIT_REQUESTS: int = 100
    RATE_LIMIT_PERIOD: int = 60  # seconds
    
    # 文档配置
    DOCS_ENABLED: bool = True
    OPENAPI_URL: Optional[str] = "/openapi.json"
    DOCS_URL: Optional[str] = "/docs"
    REDOC_URL: Optional[str] = "/redoc"
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True

@lru_cache()
def get_settings() -> Settings:
    """获取配置实例（单例模式）"""
    return Settings()

# 全局配置实例
settings = get_settings()</code></pre>
          </div>

          <div class="bg-blue-50 p-4 rounded-lg border border-blue-200">
            <h4 class="font-bold text-blue-700 mb-3">环境特定配置</h4>
            <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code># app/config/base.py
from app.core.config import Settings

class BaseConfig(Settings):
    """基础配置"""
    pass

# app/config/development.py
class DevelopmentConfig(BaseConfig):
    """开发环境配置"""
    DEBUG: bool = True
    RELOAD: bool = True
    LOG_LEVEL: str = "DEBUG"
    
    # 开发数据库
    DATABASE_URL: str = "sqlite:///./dev.db"
    DATABASE_ECHO: bool = True
    
    # 开发环境允许所有CORS
    BACKEND_CORS_ORIGINS: List[str] = ["*"]
    
    # 邮件配置（开发环境使用控制台）
    EMAILS_ENABLED: bool = False

# app/config/production.py
class ProductionConfig(BaseConfig):
    """生产环境配置"""
    DEBUG: bool = False
    RELOAD: bool = False
    LOG_LEVEL: str = "INFO"
    
    # 生产数据库
    DATABASE_URL: str  # 必须从环境变量获取
    
    # 严格的CORS配置
    BACKEND_CORS_ORIGINS: List[str] = [
        "https://yourdomain.com",
        "https://www.yourdomain.com"
    ]
    
    # 安全配置
    SECRET_KEY: str  # 必须从环境变量获取
    
    # 禁用文档
    DOCS_ENABLED: bool = False
    OPENAPI_URL: Optional[str] = None
    DOCS_URL: Optional[str] = None
    REDOC_URL: Optional[str] = None

# app/config/testing.py
class TestingConfig(BaseConfig):
    """测试环境配置"""
    DEBUG: bool = False
    TESTING: bool = True
    
    # 测试数据库
    DATABASE_URL: str = "sqlite:///./test.db"
    
    # 禁用外部服务
    EMAILS_ENABLED: bool = False
    SENTRY_DSN: Optional[str] = None
    
    # 测试专用配置
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 1  # 快速过期用于测试

# 配置工厂
def get_config(env: str = "development") -> BaseConfig:
    """根据环境获取配置"""
    configs = {
        "development": DevelopmentConfig,
        "production": ProductionConfig,
        "testing": TestingConfig,
    }
    
    return configs.get(env, DevelopmentConfig)()</code></pre>
          </div>
        </div>
      </div>

      <!-- 环境变量配置 -->
      <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-white/50 mb-8">
        <h2 class="text-3xl font-bold text-teal-800 mb-6 flex items-center">
          <svg class="w-8 h-8 mr-3 text-teal-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
          </svg>
          环境变量配置
        </h2>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-bold text-teal-800 mb-4">.env 环境变量文件</h3>
            <div class="space-y-4">
              <div class="bg-teal-50 p-4 rounded-lg border border-teal-200">
                <h4 class="font-bold text-teal-700 mb-2">.env 文件配置</h4>
                <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code># .env - 环境变量文件
# 应用配置
PROJECT_NAME=FastAPI Project
PROJECT_DESCRIPTION=A modern FastAPI application
VERSION=1.0.0
DEBUG=true
HOST=0.0.0.0
PORT=8000

# 安全配置
SECRET_KEY=your-super-secret-key-here
ACCESS_TOKEN_EXPIRE_MINUTES=30
ALGORITHM=HS256

# 数据库配置
DATABASE_URL=postgresql://username:password@localhost/fastapi_db
ASYNC_DATABASE_URL=postgresql+asyncpg://username:password@localhost/fastapi_db
DATABASE_ECHO=false
DATABASE_POOL_SIZE=5

# Redis配置
REDIS_URL=redis://localhost:6379/0
REDIS_CACHE_TTL=300

# CORS配置
BACKEND_CORS_ORIGINS=http://localhost:3000,http://localhost:8080

# 邮件配置
SMTP_TLS=true
SMTP_PORT=587
SMTP_HOST=smtp.gmail.com
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
EMAILS_FROM_EMAIL=<EMAIL>
EMAILS_FROM_NAME=Your App

# 文件上传配置
UPLOAD_DIR=uploads
MAX_UPLOAD_SIZE=10485760
ALLOWED_EXTENSIONS=jpg,jpeg,png,gif,pdf,txt

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/app.log

# 第三方服务
SENTRY_DSN=https://your-sentry-dsn

# API文档配置
DOCS_ENABLED=true
OPENAPI_URL=/openapi.json
DOCS_URL=/docs
REDOC_URL=/redoc

# OAuth配置
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret

# AWS配置
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
AWS_S3_BUCKET=your-s3-bucket

# 监控配置
PROMETHEUS_ENABLED=true
PROMETHEUS_PORT=9090</code></pre>
              </div>
            </div>
          </div>

          <div>
            <h3 class="text-xl font-bold text-teal-800 mb-4">环境变量验证</h3>
            <div class="space-y-4">
              <div class="bg-blue-50 p-4 rounded-lg border border-blue-200">
                <h4 class="font-bold text-blue-700 mb-2">高级配置验证</h4>
                <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code># 高级配置验证
from pydantic import BaseSettings, validator, Field
from typing import Optional, List, Dict, Any
import os

class AdvancedSettings(BaseSettings):
    # 必需的环境变量
    SECRET_KEY: str = Field(..., min_length=32)
    DATABASE_URL: str = Field(..., regex=r'^postgresql://.*')
    
    # 可选配置
    DEBUG: bool = False
    TESTING: bool = False
    
    # 复杂类型验证
    ALLOWED_HOSTS: List[str] = Field(default_factory=list)
    CORS_ORIGINS: List[str] = Field(default_factory=list)
    
    # 自定义验证器
    @validator('SECRET_KEY')
    def validate_secret_key(cls, v):
        if len(v) < 32:
            raise ValueError('SECRET_KEY must be at least 32 characters long')
        return v
    
    @validator('DATABASE_URL')
    def validate_database_url(cls, v):
        if not v.startswith(('postgresql://', 'sqlite://')):
            raise ValueError('DATABASE_URL must be postgresql or sqlite')
        return v
    
    @validator('ALLOWED_HOSTS', pre=True)
    def parse_allowed_hosts(cls, v):
        if isinstance(v, str):
            return [host.strip() for host in v.split(',') if host.strip()]
        return v
    
    # 环境特定验证
    @validator('DEBUG')
    def validate_debug_in_production(cls, v, values):
        if os.getenv('ENVIRONMENT') == 'production' and v:
            raise ValueError('DEBUG cannot be True in production')
        return v
    
    # 依赖验证
    @validator('SMTP_PASSWORD')
    def validate_smtp_config(cls, v, values):
        smtp_host = values.get('SMTP_HOST')
        if smtp_host and not v:
            raise ValueError('SMTP_PASSWORD required when SMTP_HOST is set')
        return v
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True
        
        # 环境变量前缀
        env_prefix = "FASTAPI_"
        
        # 嵌套环境变量
        env_nested_delimiter = "__"
        
        # 字段别名
        fields = {
            'database_url': {
                'env': ['DATABASE_URL', 'DB_URL']
            }
        }

# 配置加载和验证
def load_config() -> AdvancedSettings:
    """加载和验证配置"""
    try:
        settings = AdvancedSettings()
        return settings
    except Exception as e:
        print(f"Configuration error: {e}")
        raise

# 配置检查工具
def check_config():
    """检查配置完整性"""
    try:
        settings = load_config()
        print("✅ Configuration is valid")
        return True
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        return False

if __name__ == "__main__":
    check_config()</code></pre>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 数据库和ORM配置 -->
      <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-white/50 mb-8">
        <h2 class="text-3xl font-bold text-cyan-800 mb-6 flex items-center">
          <svg class="w-8 h-8 mr-3 text-cyan-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"/>
          </svg>
          数据库和ORM配置
        </h2>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-bold text-cyan-800 mb-4">SQLAlchemy配置</h3>
            <div class="space-y-4">
              <div class="bg-cyan-50 p-4 rounded-lg border border-cyan-200">
                <h4 class="font-bold text-cyan-700 mb-2">数据库配置</h4>
                <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code># app/database.py
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool
from app.core.config import settings

# 同步数据库配置
engine = create_engine(
    settings.DATABASE_URL,
    echo=settings.DATABASE_ECHO,
    pool_size=settings.DATABASE_POOL_SIZE,
    max_overflow=settings.DATABASE_MAX_OVERFLOW,
    pool_pre_ping=True,
    pool_recycle=3600,
    connect_args={
        "check_same_thread": False  # SQLite only
    } if "sqlite" in settings.DATABASE_URL else {}
)

SessionLocal = sessionmaker(
    autocommit=False,
    autoflush=False,
    bind=engine
)

Base = declarative_base()

# 异步数据库配置
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession

async_engine = create_async_engine(
    settings.ASYNC_DATABASE_URL,
    echo=settings.DATABASE_ECHO,
    pool_size=settings.DATABASE_POOL_SIZE,
    max_overflow=settings.DATABASE_MAX_OVERFLOW,
    pool_pre_ping=True,
    pool_recycle=3600,
)

AsyncSessionLocal = sessionmaker(
    async_engine,
    class_=AsyncSession,
    expire_on_commit=False
)

# 数据库依赖
def get_db():
    """同步数据库会话依赖"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

async def get_async_db():
    """异步数据库会话依赖"""
    async with AsyncSessionLocal() as session:
        yield session

# 数据库初始化
def create_tables():
    """创建数据库表"""
    Base.metadata.create_all(bind=engine)

async def create_async_tables():
    """异步创建数据库表"""
    async with async_engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)</code></pre>
              </div>
            </div>
          </div>

          <div>
            <h3 class="text-xl font-bold text-cyan-800 mb-4">Alembic迁移配置</h3>
            <div class="space-y-4">
              <div class="bg-blue-50 p-4 rounded-lg border border-blue-200">
                <h4 class="font-bold text-blue-700 mb-2">alembic.ini配置</h4>
                <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code># alembic.ini
[alembic]
# 迁移脚本位置
script_location = alembic

# 模板文件
file_template = %%(year)d_%%(month).2d_%%(day).2d_%%(hour).2d%%(minute).2d-%%(rev)s_%%(slug)s

# 时区设置
timezone = UTC

# 数据库URL（从环境变量读取）
sqlalchemy.url =

# 日志配置
[loggers]
keys = root,sqlalchemy,alembic

[handlers]
keys = console

[formatters]
keys = generic

[logger_root]
level = WARN
handlers = console
qualname =

[logger_sqlalchemy]
level = WARN
handlers =
qualname = sqlalchemy.engine

[logger_alembic]
level = INFO
handlers =
qualname = alembic

[handler_console]
class = StreamHandler
args = (sys.stderr,)
level = NOTSET
formatter = generic

[formatter_generic]
format = %(levelname)-5.5s [%(name)s] %(message)s
datefmt = %H:%M:%S

# alembic/env.py
from logging.config import fileConfig
from sqlalchemy import engine_from_config, pool
from alembic import context
from app.core.config import settings
from app.database import Base

# Alembic配置对象
config = context.config

# 设置数据库URL
config.set_main_option("sqlalchemy.url", settings.DATABASE_URL)

# 解释配置文件的日志记录
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# 目标元数据
target_metadata = Base.metadata

def run_migrations_offline():
    """离线模式运行迁移"""
    url = config.get_main_option("sqlalchemy.url")
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
    )

    with context.begin_transaction():
        context.run_migrations()

def run_migrations_online():
    """在线模式运行迁移"""
    connectable = engine_from_config(
        config.get_section(config.config_ini_section),
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
    )

    with connectable.connect() as connection:
        context.configure(
            connection=connection,
            target_metadata=target_metadata
        )

        with context.begin_transaction():
            context.run_migrations()

if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()</code></pre>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 中间件和安全配置 -->
      <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-white/50 mb-8">
        <h2 class="text-3xl font-bold text-purple-800 mb-6 flex items-center">
          <svg class="w-8 h-8 mr-3 text-purple-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"/>
          </svg>
          中间件和安全配置
        </h2>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-bold text-purple-800 mb-4">中间件配置</h3>
            <div class="space-y-4">
              <div class="bg-purple-50 p-4 rounded-lg border border-purple-200">
                <h4 class="font-bold text-purple-700 mb-2">应用中间件</h4>
                <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code># app/middleware/__init__.py
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from starlette.middleware.sessions import SessionMiddleware
from app.core.config import settings

def setup_middleware(app: FastAPI) -> None:
    """设置应用中间件"""

    # CORS中间件
    if settings.BACKEND_CORS_ORIGINS:
        app.add_middleware(
            CORSMiddleware,
            allow_origins=[str(origin) for origin in settings.BACKEND_CORS_ORIGINS],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
            expose_headers=["*"],
        )

    # 压缩中间件
    app.add_middleware(GZipMiddleware, minimum_size=1000)

    # 受信任主机中间件
    if settings.ALLOWED_HOSTS:
        app.add_middleware(
            TrustedHostMiddleware,
            allowed_hosts=settings.ALLOWED_HOSTS
        )

    # 会话中间件
    app.add_middleware(
        SessionMiddleware,
        secret_key=settings.SECRET_KEY,
        max_age=settings.SESSION_MAX_AGE,
        same_site="lax",
        https_only=not settings.DEBUG,
    )

# 自定义中间件
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.requests import Request
from starlette.responses import Response
import time
import uuid

class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """请求日志中间件"""

    async def dispatch(self, request: Request, call_next):
        # 生成请求ID
        request_id = str(uuid.uuid4())
        request.state.request_id = request_id

        # 记录请求开始时间
        start_time = time.time()

        # 处理请求
        response = await call_next(request)

        # 计算处理时间
        process_time = time.time() - start_time

        # 添加响应头
        response.headers["X-Request-ID"] = request_id
        response.headers["X-Process-Time"] = str(process_time)

        # 记录日志
        logger.info(
            f"Request {request_id} - "
            f"{request.method} {request.url} - "
            f"Status: {response.status_code} - "
            f"Time: {process_time:.4f}s"
        )

        return response

class RateLimitMiddleware(BaseHTTPMiddleware):
    """限流中间件"""

    def __init__(self, app, calls: int = 100, period: int = 60):
        super().__init__(app)
        self.calls = calls
        self.period = period
        self.clients = {}

    async def dispatch(self, request: Request, call_next):
        client_ip = request.client.host
        current_time = time.time()

        # 清理过期记录
        if client_ip in self.clients:
            self.clients[client_ip] = [
                timestamp for timestamp in self.clients[client_ip]
                if current_time - timestamp < self.period
            ]
        else:
            self.clients[client_ip] = []

        # 检查限流
        if len(self.clients[client_ip]) >= self.calls:
            return Response(
                content="Rate limit exceeded",
                status_code=429,
                headers={"Retry-After": str(self.period)}
            )

        # 记录请求时间
        self.clients[client_ip].append(current_time)

        return await call_next(request)</code></pre>
              </div>
            </div>
          </div>

          <div>
            <h3 class="text-xl font-bold text-purple-800 mb-4">安全配置</h3>
            <div class="space-y-4">
              <div class="bg-blue-50 p-4 rounded-lg border border-blue-200">
                <h4 class="font-bold text-blue-700 mb-2">安全中间件</h4>
                <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code># app/middleware/security.py
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.requests import Request
from starlette.responses import Response

class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """安全头中间件"""

    async def dispatch(self, request: Request, call_next):
        response = await call_next(request)

        # 安全头配置
        security_headers = {
            "X-Content-Type-Options": "nosniff",
            "X-Frame-Options": "DENY",
            "X-XSS-Protection": "1; mode=block",
            "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
            "Referrer-Policy": "strict-origin-when-cross-origin",
            "Permissions-Policy": "geolocation=(), microphone=(), camera=()",
            "Content-Security-Policy": (
                "default-src 'self'; "
                "script-src 'self' 'unsafe-inline'; "
                "style-src 'self' 'unsafe-inline'; "
                "img-src 'self' data: https:; "
                "font-src 'self'; "
                "connect-src 'self'; "
                "frame-ancestors 'none';"
            )
        }

        # 添加安全头
        for header, value in security_headers.items():
            response.headers[header] = value

        return response

# JWT配置
from jose import JWTError, jwt
from datetime import datetime, timedelta
from typing import Optional

class JWTConfig:
    """JWT配置类"""

    def __init__(self, settings):
        self.secret_key = settings.SECRET_KEY
        self.algorithm = settings.ALGORITHM
        self.access_token_expire_minutes = settings.ACCESS_TOKEN_EXPIRE_MINUTES
        self.refresh_token_expire_minutes = settings.REFRESH_TOKEN_EXPIRE_MINUTES

    def create_access_token(self, data: dict, expires_delta: Optional[timedelta] = None):
        """创建访问令牌"""
        to_encode = data.copy()
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=self.access_token_expire_minutes)

        to_encode.update({"exp": expire, "type": "access"})
        encoded_jwt = jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)
        return encoded_jwt

    def create_refresh_token(self, data: dict):
        """创建刷新令牌"""
        to_encode = data.copy()
        expire = datetime.utcnow() + timedelta(minutes=self.refresh_token_expire_minutes)
        to_encode.update({"exp": expire, "type": "refresh"})
        encoded_jwt = jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)
        return encoded_jwt

    def verify_token(self, token: str, token_type: str = "access"):
        """验证令牌"""
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            if payload.get("type") != token_type:
                return None
            return payload
        except JWTError:
            return None

# OAuth2配置
from fastapi.security import OAuth2PasswordBearer, OAuth2PasswordRequestForm

oauth2_scheme = OAuth2PasswordBearer(
    tokenUrl=f"{settings.API_V1_STR}/auth/login",
    scopes={
        "read": "Read access",
        "write": "Write access",
        "admin": "Admin access"
    }
)</code></pre>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 部署配置 -->
      <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-white/50 mb-8">
        <h2 class="text-3xl font-bold text-pink-800 mb-6 flex items-center">
          <svg class="w-8 h-8 mr-3 text-pink-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
          </svg>
          部署配置
        </h2>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-bold text-pink-800 mb-4">Gunicorn配置</h3>
            <div class="space-y-4">
              <div class="bg-pink-50 p-4 rounded-lg border border-pink-200">
                <h4 class="font-bold text-pink-700 mb-2">gunicorn.conf.py</h4>
                <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code># gunicorn.conf.py
import multiprocessing
import os

# 服务器配置
bind = f"0.0.0.0:{os.getenv('PORT', 8000)}"
workers = multiprocessing.cpu_count() * 2 + 1
worker_class = "uvicorn.workers.UvicornWorker"
worker_connections = 1000
max_requests = 1000
max_requests_jitter = 100

# 进程配置
preload_app = True
daemon = False
user = None
group = None
tmp_upload_dir = None

# 日志配置
accesslog = "-"
errorlog = "-"
loglevel = os.getenv("LOG_LEVEL", "info")
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s" %(D)s'

# 进程名称
proc_name = "fastapi_app"

# 超时配置
timeout = 30
keepalive = 2
graceful_timeout = 30

# SSL配置（如果需要）
keyfile = os.getenv("SSL_KEYFILE")
certfile = os.getenv("SSL_CERTFILE")

# 性能调优
worker_tmp_dir = "/dev/shm"  # 使用内存文件系统
forwarded_allow_ips = "*"
secure_scheme_headers = {
    "X-FORWARDED-PROTOCOL": "ssl",
    "X-FORWARDED-PROTO": "https",
    "X-FORWARDED-SSL": "on"
}

# 钩子函数
def on_starting(server):
    """服务器启动时执行"""
    server.log.info("Starting FastAPI application")

def on_reload(server):
    """重载时执行"""
    server.log.info("Reloading FastAPI application")

def worker_int(worker):
    """工作进程中断时执行"""
    worker.log.info("Worker received INT or QUIT signal")

def pre_fork(server, worker):
    """fork工作进程前执行"""
    server.log.info("Worker spawned (pid: %s)", worker.pid)

def post_fork(server, worker):
    """fork工作进程后执行"""
    server.log.info("Worker spawned (pid: %s)", worker.pid)

def worker_abort(worker):
    """工作进程异常退出时执行"""
    worker.log.info("Worker received SIGABRT signal")</code></pre>
              </div>
            </div>
          </div>

          <div>
            <h3 class="text-xl font-bold text-pink-800 mb-4">Docker配置</h3>
            <div class="space-y-4">
              <div class="bg-blue-50 p-4 rounded-lg border border-blue-200">
                <h4 class="font-bold text-blue-700 mb-2">Dockerfile</h4>
                <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code># Dockerfile
FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH=/app

# 安装系统依赖
RUN apt-get update \
    && apt-get install -y --no-install-recommends \
        build-essential \
        libpq-dev \
        curl \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir --upgrade pip \
    && pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建非root用户
RUN adduser --disabled-password --gecos '' appuser \
    && chown -R appuser:appuser /app
USER appuser

# 暴露端口
EXPOSE 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# 启动命令
CMD ["gunicorn", "app.main:app", "-c", "gunicorn.conf.py"]

# docker-compose.yml
version: '3.8'

services:
  web:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=**************************************/fastapi_db
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - db
      - redis
    volumes:
      - ./uploads:/app/uploads
    restart: unless-stopped

  db:
    image: postgres:15
    environment:
      - POSTGRES_DB=fastapi_db
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - web
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:</code></pre>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FastAPIConfig'
}
</script>

<style scoped>
@keyframes blob {
  0% { transform: translate(0px, 0px) scale(1); }
  33% { transform: translate(30px, -50px) scale(1.1); }
  66% { transform: translate(-20px, 20px) scale(0.9); }
  100% { transform: translate(0px, 0px) scale(1); }
}

.animate-blob {
  animation: blob 7s infinite;
}

.animation-delay-2000 {
  animation-delay: 2s;
}
</style>
