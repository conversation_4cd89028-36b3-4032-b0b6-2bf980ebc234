<template>
  <div class="min-h-screen bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50 flex flex-col relative">
    <!-- 背景装饰 -->
    <div class="absolute inset-0 overflow-hidden pointer-events-none">
      <div class="absolute -top-40 -right-40 w-80 h-80 bg-green-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob"></div>
      <div class="absolute -bottom-40 -left-40 w-80 h-80 bg-emerald-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000"></div>
    </div>

    <div class="max-w-7xl mx-auto py-12 px-4 sm:px-6 md:px-8 w-full relative z-10">
      <!-- 返回按钮 -->
      <div class="mb-8">
        <router-link to="/web/framework/python/django" class="inline-flex items-center text-green-600 hover:text-green-700 transition-colors duration-200">
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
          </svg>
          返回 Django 框架
        </router-link>
      </div>

      <!-- 页面头部 -->
      <div class="text-center mb-12">
        <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-600 rounded-full mb-6 shadow-lg">
          <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"/>
          </svg>
        </div>
        <h1 class="text-4xl font-extrabold text-transparent bg-clip-text bg-gradient-to-r from-green-600 to-emerald-600 mb-4">
          Django 安全加固
        </h1>
        <p class="text-xl text-gray-700 max-w-4xl mx-auto leading-relaxed">
          🛡️ 全面的Django应用安全加固方案和最佳实践
        </p>
      </div>

      <!-- 基础安全配置 -->
      <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-white/50 mb-8">
        <h2 class="text-3xl font-bold text-green-800 mb-6 flex items-center">
          <svg class="w-8 h-8 mr-3 text-green-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
            <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
          </svg>
          基础安全配置
        </h2>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-bold text-green-800 mb-4">生产环境配置</h3>
            <div class="space-y-4">
              <div class="bg-red-50 p-4 rounded-lg border border-red-200">
                <h4 class="font-bold text-red-700 mb-2">关闭调试模式</h4>
                <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code># settings/production.py
import os
from decouple import config

# 关闭调试模式
DEBUG = False

# 设置允许的主机
ALLOWED_HOSTS = [
    'yourdomain.com',
    'www.yourdomain.com',
    '127.0.0.1',
]

# 安全的SECRET_KEY
SECRET_KEY = config('SECRET_KEY')

# 移除开发工具
INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    
    # 移除调试工具
    # 'debug_toolbar',
    # 'django_extensions',
    
    # 生产环境应用
    'your_app',
]

# 移除调试中间件
MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    
    # 移除调试中间件
    # 'debug_toolbar.middleware.DebugToolbarMiddleware',
]</code></pre>
              </div>

              <div class="bg-blue-50 p-4 rounded-lg border border-blue-200">
                <h4 class="font-bold text-blue-700 mb-2">安全头配置</h4>
                <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code># 安全头配置
SECURE_BROWSER_XSS_FILTER = True
SECURE_CONTENT_TYPE_NOSNIFF = True
SECURE_HSTS_INCLUDE_SUBDOMAINS = True
SECURE_HSTS_PRELOAD = True
SECURE_HSTS_SECONDS = 31536000  # 1年

# HTTPS配置
SECURE_SSL_REDIRECT = True
SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')

# Cookie安全
SESSION_COOKIE_SECURE = True
SESSION_COOKIE_HTTPONLY = True
SESSION_COOKIE_SAMESITE = 'Lax'
CSRF_COOKIE_SECURE = True
CSRF_COOKIE_HTTPONLY = True

# X-Frame-Options
X_FRAME_OPTIONS = 'DENY'

# 自定义安全中间件
class SecurityHeadersMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response
    
    def __call__(self, request):
        response = self.get_response(request)
        
        # 添加安全头
        response['X-Content-Type-Options'] = 'nosniff'
        response['X-Frame-Options'] = 'DENY'
        response['X-XSS-Protection'] = '1; mode=block'
        response['Referrer-Policy'] = 'strict-origin-when-cross-origin'
        response['Content-Security-Policy'] = "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'"
        
        return response</code></pre>
              </div>
            </div>
          </div>

          <div>
            <h3 class="text-xl font-bold text-green-800 mb-4">数据库安全配置</h3>
            <div class="space-y-4">
              <div class="bg-green-50 p-4 rounded-lg border border-green-200">
                <h4 class="font-bold text-green-700 mb-2">数据库连接安全</h4>
                <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code># 安全的数据库配置
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': config('DB_NAME'),
        'USER': config('DB_USER'),
        'PASSWORD': config('DB_PASSWORD'),
        'HOST': config('DB_HOST', default='localhost'),
        'PORT': config('DB_PORT', default='5432'),
        'OPTIONS': {
            'sslmode': 'require',
            'connect_timeout': 10,
            'options': '-c default_transaction_isolation=serializable'
        },
        'CONN_MAX_AGE': 60,
        'CONN_HEALTH_CHECKS': True,
    }
}

# 数据库查询安全
DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# 防止SQL注入的ORM使用
class SecureQuerySet(models.QuerySet):
    def filter_safe(self, **kwargs):
        # 白名单字段验证
        allowed_fields = ['id', 'name', 'email', 'created_at']
        for field in kwargs.keys():
            if field.split('__')[0] not in allowed_fields:
                raise ValueError(f"Field {field} not allowed")
        return self.filter(**kwargs)

class SecureManager(models.Manager):
    def get_queryset(self):
        return SecureQuerySet(self.model, using=self._db)
    
    def safe_filter(self, **kwargs):
        return self.get_queryset().filter_safe(**kwargs)</code></pre>
              </div>

              <div class="bg-purple-50 p-4 rounded-lg border border-purple-200">
                <h4 class="font-bold text-purple-700 mb-2">SQL注入防护</h4>
                <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code># 安全的查询方式
from django.db import models
from django.db.models import Q

class PostManager(models.Manager):
    def search_safe(self, query, user):
        # 使用参数化查询
        return self.filter(
            Q(title__icontains=query) | Q(content__icontains=query),
            author=user,
            status='published'
        )
    
    def get_by_id_safe(self, post_id):
        # 验证参数类型
        try:
            post_id = int(post_id)
        except (ValueError, TypeError):
            return None
        
        return self.filter(id=post_id).first()

# 避免危险的查询方法
def safe_search_view(request):
    query = request.GET.get('q', '')
    
    # 安全：使用ORM参数化查询
    posts = Post.objects.filter(title__icontains=query)
    
    # 危险：避免使用raw SQL
    # posts = Post.objects.raw("SELECT * FROM blog_post WHERE title LIKE '%{}%'".format(query))
    
    # 危险：避免使用extra()
    # posts = Post.objects.extra(where=["title LIKE '%{}%'".format(query)])
    
    return render(request, 'search.html', {'posts': posts})</code></pre>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 输入验证和过滤 -->
      <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-white/50 mb-8">
        <h2 class="text-3xl font-bold text-emerald-800 mb-6 flex items-center">
          <svg class="w-8 h-8 mr-3 text-emerald-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"/>
          </svg>
          输入验证和过滤
        </h2>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-bold text-emerald-800 mb-4">表单验证安全</h3>
            <div class="space-y-4">
              <div class="bg-blue-50 p-3 rounded border border-blue-200">
                <strong class="text-blue-700">安全的表单验证：</strong>
                <pre class="bg-white p-2 rounded mt-2 text-xs"><code># forms.py
from django import forms
from django.core.exceptions import ValidationError
import re

class SecureContactForm(forms.Form):
    name = forms.CharField(
        max_length=100,
        validators=[
            lambda value: self.validate_name(value)
        ]
    )
    email = forms.EmailField()
    message = forms.CharField(
        widget=forms.Textarea,
        validators=[
            lambda value: self.validate_message(value)
        ]
    )
    
    def validate_name(self, value):
        # 只允许字母、数字、空格和常见标点
        if not re.match(r'^[a-zA-Z0-9\s\-_.]+$', value):
            raise ValidationError('姓名包含非法字符')
        return value
    
    def validate_message(self, value):
        # 检查恶意脚本
        dangerous_patterns = [
            r'&lt;script.*?&gt;.*?&lt;/script&gt;',
            r'javascript:',
            r'vbscript:',
            r'on\w+\s*=',
        ]
        
        for pattern in dangerous_patterns:
            if re.search(pattern, value, re.IGNORECASE):
                raise ValidationError('消息包含危险内容')
        
        return value
    
    def clean(self):
        cleaned_data = super().clean()
        
        # 全局验证
        name = cleaned_data.get('name')
        message = cleaned_data.get('message')
        
        if name and message and name.lower() in message.lower():
            raise ValidationError('消息不能包含姓名')
        
        return cleaned_data</code></pre>
              </div>
            </div>
          </div>

          <div>
            <h3 class="text-xl font-bold text-emerald-800 mb-4">模板安全防护</h3>
            <div class="space-y-4">
              <div class="bg-green-50 p-3 rounded border border-green-200">
                <strong class="text-green-700">模板注入防护：</strong>
                <pre class="bg-white p-2 rounded mt-2 text-xs"><code># 安全的模板渲染
from django.template import Template, Context
from django.template.loader import get_template
from django.utils.safestring import mark_safe
import html

def safe_template_render(request):
    user_input = request.GET.get('content', '')
    
    # 安全：HTML转义用户输入
    safe_content = html.escape(user_input)
    
    # 安全：使用预定义模板
    template = get_template('safe_template.html')
    context = {'content': safe_content}
    
    return HttpResponse(template.render(context, request))

# 自定义安全过滤器
from django import template

register = template.Library()

@register.filter
def safe_html(value):
    """安全的HTML过滤器"""
    import bleach
    
    allowed_tags = ['p', 'br', 'strong', 'em', 'ul', 'ol', 'li']
    allowed_attributes = {}
    
    return mark_safe(bleach.clean(value, tags=allowed_tags, attributes=allowed_attributes))

@register.filter
def strip_dangerous(value):
    """移除危险内容"""
    dangerous_patterns = [
        r'&lt;script.*?&gt;.*?&lt;/script&gt;',
        r'javascript:',
        r'vbscript:',
        r'on\w+\s*=',
    ]
    
    for pattern in dangerous_patterns:
        value = re.sub(pattern, '', value, flags=re.IGNORECASE)
    
    return value

# 在模板中使用
# &#123;&#123; user_content|safe_html &#125;&#125;
# &#123;&#123; user_input|strip_dangerous|escape &#125;&#125;</code></pre>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 认证和权限控制 -->
      <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-white/50 mb-8">
        <h2 class="text-3xl font-bold text-teal-800 mb-6 flex items-center">
          <svg class="w-8 h-8 mr-3 text-teal-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M15 7a2 2 0 012 2m0 0a2 2 0 012 2m-2-2a2 2 0 00-2 2m2-2V5a2 2 0 00-2-2m0 0H9a2 2 0 00-2 2v0a2 2 0 00-2 2m2-2a2 2 0 012-2m0 0h6"/>
          </svg>
          认证和权限控制
        </h2>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-bold text-teal-800 mb-4">用户认证安全</h3>
            <div class="space-y-4">
              <div class="bg-teal-50 p-4 rounded-lg border border-teal-200">
                <h4 class="font-bold text-teal-700 mb-2">密码安全配置</h4>
                <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code># 密码验证器配置
AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
        'OPTIONS': {
            'user_attributes': ('username', 'email', 'first_name', 'last_name'),
            'max_similarity': 0.7,
        }
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
        'OPTIONS': {
            'min_length': 12,
        }
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
    {
        'NAME': 'myapp.validators.CustomPasswordValidator',
    },
]

# 自定义密码验证器
from django.core.exceptions import ValidationError
import re

class CustomPasswordValidator:
    def validate(self, password, user=None):
        # 必须包含大小写字母、数字和特殊字符
        if not re.search(r'[A-Z]', password):
            raise ValidationError('密码必须包含大写字母')
        if not re.search(r'[a-z]', password):
            raise ValidationError('密码必须包含小写字母')
        if not re.search(r'\d', password):
            raise ValidationError('密码必须包含数字')
        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            raise ValidationError('密码必须包含特殊字符')

    def get_help_text(self):
        return '密码必须包含大小写字母、数字和特殊字符'

# 登录安全
LOGIN_URL = '/accounts/login/'
LOGIN_REDIRECT_URL = '/'
LOGOUT_REDIRECT_URL = '/'

# 会话安全
SESSION_COOKIE_AGE = 3600  # 1小时
SESSION_SAVE_EVERY_REQUEST = True
SESSION_EXPIRE_AT_BROWSER_CLOSE = True</code></pre>
              </div>

              <div class="bg-blue-50 p-4 rounded-lg border border-blue-200">
                <h4 class="font-bold text-blue-700 mb-2">登录保护机制</h4>
                <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code># 登录失败保护
from django.contrib.auth import authenticate, login
from django.core.cache import cache
from django.http import HttpResponseForbidden
import time

class LoginProtectionMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        if request.path == '/accounts/login/' and request.method == 'POST':
            ip = self.get_client_ip(request)

            # 检查登录尝试次数
            attempts_key = f'login_attempts_{ip}'
            attempts = cache.get(attempts_key, 0)

            if attempts >= 5:
                return HttpResponseForbidden('登录尝试次数过多，请稍后再试')

        response = self.get_response(request)
        return response

    def get_client_ip(self, request):
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip

# 安全的登录视图
def secure_login_view(request):
    if request.method == 'POST':
        username = request.POST.get('username')
        password = request.POST.get('password')

        # 记录登录尝试
        ip = get_client_ip(request)
        attempts_key = f'login_attempts_{ip}'
        attempts = cache.get(attempts_key, 0)

        user = authenticate(request, username=username, password=password)

        if user is not None:
            # 登录成功，清除失败记录
            cache.delete(attempts_key)
            login(request, user)

            # 记录成功登录
            logger.info(f'User {username} logged in from {ip}')

            return redirect('dashboard')
        else:
            # 登录失败，增加计数
            cache.set(attempts_key, attempts + 1, 300)  # 5分钟

            # 记录失败尝试
            logger.warning(f'Failed login attempt for {username} from {ip}')

            messages.error(request, '用户名或密码错误')

    return render(request, 'registration/login.html')</code></pre>
              </div>
            </div>
          </div>

          <div>
            <h3 class="text-xl font-bold text-teal-800 mb-4">权限控制系统</h3>
            <div class="space-y-4">
              <div class="bg-purple-50 p-4 rounded-lg border border-purple-200">
                <h4 class="font-bold text-purple-700 mb-2">基于角色的权限控制</h4>
                <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code># models.py
from django.contrib.auth.models import AbstractUser, Group, Permission
from django.db import models

class User(AbstractUser):
    role = models.CharField(max_length=20, choices=[
        ('admin', '管理员'),
        ('editor', '编辑'),
        ('viewer', '查看者'),
    ], default='viewer')

    def has_role(self, role):
        return self.role == role

    def can_edit_post(self, post):
        if self.role == 'admin':
            return True
        elif self.role == 'editor':
            return post.author == self
        return False

# 权限装饰器
from functools import wraps
from django.core.exceptions import PermissionDenied

def require_role(role):
    def decorator(view_func):
        @wraps(view_func)
        def _wrapped_view(request, *args, **kwargs):
            if not request.user.is_authenticated:
                return redirect('login')

            if not request.user.has_role(role):
                raise PermissionDenied

            return view_func(request, *args, **kwargs)
        return _wrapped_view
    return decorator

def require_permission(permission):
    def decorator(view_func):
        @wraps(view_func)
        def _wrapped_view(request, *args, **kwargs):
            if not request.user.has_perm(permission):
                raise PermissionDenied

            return view_func(request, *args, **kwargs)
        return _wrapped_view
    return decorator

# 使用权限装饰器
@require_role('admin')
def admin_dashboard(request):
    return render(request, 'admin/dashboard.html')

@require_permission('blog.add_post')
def create_post(request):
    # 创建文章逻辑
    pass</code></pre>
              </div>

              <div class="bg-green-50 p-4 rounded-lg border border-green-200">
                <h4 class="font-bold text-green-700 mb-2">对象级权限控制</h4>
                <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code># 对象级权限检查
class PostPermissionMixin:
    def dispatch(self, request, *args, **kwargs):
        post = self.get_object()

        if not self.has_permission(request.user, post):
            raise PermissionDenied

        return super().dispatch(request, *args, **kwargs)

    def has_permission(self, user, post):
        # 管理员可以访问所有文章
        if user.has_role('admin'):
            return True

        # 作者可以访问自己的文章
        if post.author == user:
            return True

        # 已发布的文章所有人都可以查看
        if self.request.method == 'GET' and post.status == 'published':
            return True

        return False

class PostUpdateView(PostPermissionMixin, UpdateView):
    model = Post
    form_class = PostForm
    template_name = 'blog/post_form.html'

# 中间件级权限控制
class PermissionMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # 检查API访问权限
        if request.path.startswith('/api/'):
            if not self.check_api_permission(request):
                return JsonResponse({'error': 'Permission denied'}, status=403)

        response = self.get_response(request)
        return response

    def check_api_permission(self, request):
        # API密钥验证
        api_key = request.META.get('HTTP_X_API_KEY')
        if not api_key:
            return False

        # 验证API密钥
        return APIKey.objects.filter(key=api_key, is_active=True).exists()</code></pre>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 文件上传和静态文件安全 -->
      <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-white/50 mb-8">
        <h2 class="text-3xl font-bold text-cyan-800 mb-6 flex items-center">
          <svg class="w-8 h-8 mr-3 text-cyan-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"/>
          </svg>
          文件上传和静态文件安全
        </h2>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-bold text-cyan-800 mb-4">安全文件上传</h3>
            <div class="space-y-4">
              <div class="bg-cyan-50 p-4 rounded-lg border border-cyan-200">
                <h4 class="font-bold text-cyan-700 mb-2">文件上传验证</h4>
                <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code># 安全的文件上传处理
import os
import magic
from django.core.files.storage import default_storage
from django.core.exceptions import ValidationError

class SecureFileUploadHandler:
    ALLOWED_EXTENSIONS = ['.jpg', '.jpeg', '.png', '.gif', '.pdf', '.doc', '.docx']
    MAX_FILE_SIZE = 5 * 1024 * 1024  # 5MB

    def __init__(self):
        self.mime_types = {
            '.jpg': 'image/jpeg',
            '.jpeg': 'image/jpeg',
            '.png': 'image/png',
            '.gif': 'image/gif',
            '.pdf': 'application/pdf',
        }

    def validate_file(self, uploaded_file):
        # 检查文件大小
        if uploaded_file.size > self.MAX_FILE_SIZE:
            raise ValidationError('文件大小超过限制')

        # 检查文件扩展名
        file_ext = os.path.splitext(uploaded_file.name)[1].lower()
        if file_ext not in self.ALLOWED_EXTENSIONS:
            raise ValidationError('不允许的文件类型')

        # 检查MIME类型
        file_content = uploaded_file.read(1024)
        uploaded_file.seek(0)  # 重置文件指针

        detected_type = magic.from_buffer(file_content, mime=True)
        expected_type = self.mime_types.get(file_ext)

        if expected_type and detected_type != expected_type:
            raise ValidationError('文件类型不匹配')

        # 检查文件头
        if not self.check_file_header(uploaded_file, file_ext):
            raise ValidationError('文件头验证失败')

        return True

    def check_file_header(self, uploaded_file, file_ext):
        uploaded_file.seek(0)
        header = uploaded_file.read(10)
        uploaded_file.seek(0)

        headers = {
            '.jpg': b'\xff\xd8\xff',
            '.jpeg': b'\xff\xd8\xff',
            '.png': b'\x89\x50\x4e\x47',
            '.gif': b'GIF',
            '.pdf': b'%PDF',
        }

        expected_header = headers.get(file_ext)
        if expected_header:
            return header.startswith(expected_header)

        return True

    def generate_safe_filename(self, filename):
        # 生成安全的文件名
        import uuid
        import time

        name, ext = os.path.splitext(filename)
        safe_name = re.sub(r'[^a-zA-Z0-9_-]', '_', name)[:50]
        timestamp = int(time.time())
        unique_id = str(uuid.uuid4())[:8]

        return f"{safe_name}_{timestamp}_{unique_id}{ext}"

# 在表单中使用
class FileUploadForm(forms.Form):
    file = forms.FileField()

    def clean_file(self):
        file = self.cleaned_data['file']
        handler = SecureFileUploadHandler()
        handler.validate_file(file)
        return file</code></pre>
              </div>
            </div>
          </div>

          <div>
            <h3 class="text-xl font-bold text-cyan-800 mb-4">静态文件安全</h3>
            <div class="space-y-4">
              <div class="bg-blue-50 p-4 rounded-lg border border-blue-200">
                <h4 class="font-bold text-blue-700 mb-2">静态文件配置</h4>
                <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code># 安全的静态文件配置
STATIC_URL = '/static/'
STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')

# 媒体文件配置
MEDIA_URL = '/media/'
MEDIA_ROOT = os.path.join(BASE_DIR, 'media')

# 文件存储安全
DEFAULT_FILE_STORAGE = 'myapp.storage.SecureFileStorage'

class SecureFileStorage(FileSystemStorage):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    def _save(self, name, content):
        # 生成安全的文件路径
        name = self.get_safe_name(name)
        return super()._save(name, content)

    def get_safe_name(self, name):
        # 防止路径遍历
        name = os.path.basename(name)

        # 移除危险字符
        name = re.sub(r'[^\w\s-.]', '', name)

        return name

    def url(self, name):
        # 添加访问控制
        if self.is_sensitive_file(name):
            return reverse('secure_file_view', args=[name])

        return super().url(name)

    def is_sensitive_file(self, name):
        sensitive_patterns = [
            r'\.py$',
            r'\.env$',
            r'\.conf$',
            r'\.log$',
        ]

        for pattern in sensitive_patterns:
            if re.search(pattern, name, re.IGNORECASE):
                return True

        return False

# 安全的文件访问视图
@login_required
def secure_file_view(request, filename):
    # 验证用户权限
    if not request.user.has_perm('view_file'):
        raise PermissionDenied

    # 验证文件路径
    safe_path = os.path.join(settings.MEDIA_ROOT, filename)
    if not safe_path.startswith(settings.MEDIA_ROOT):
        raise Http404

    # 检查文件是否存在
    if not os.path.exists(safe_path):
        raise Http404

    # 返回文件
    return FileResponse(open(safe_path, 'rb'))</code></pre>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 日志和监控 -->
      <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-white/50 mb-8">
        <h2 class="text-3xl font-bold text-indigo-800 mb-6 flex items-center">
          <svg class="w-8 h-8 mr-3 text-indigo-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
          </svg>
          安全日志和监控
        </h2>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-bold text-indigo-800 mb-4">安全事件日志</h3>
            <div class="space-y-4">
              <div class="bg-indigo-50 p-4 rounded-lg border border-indigo-200">
                <h4 class="font-bold text-indigo-700 mb-2">日志配置</h4>
                <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code># 安全日志配置
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'security': {
            'format': '{asctime} {levelname} {name} {message}',
            'style': '{',
        },
        'json': {
            'format': '{"timestamp": "%(asctime)s", "level": "%(levelname)s", "logger": "%(name)s", "message": "%(message)s", "user": "%(user)s", "ip": "%(ip)s"}',
        },
    },
    'handlers': {
        'security_file': {
            'level': 'WARNING',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': '/var/log/django/security.log',
            'maxBytes': 1024*1024*15,  # 15MB
            'backupCount': 10,
            'formatter': 'json',
        },
        'auth_file': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': '/var/log/django/auth.log',
            'maxBytes': 1024*1024*15,
            'backupCount': 10,
            'formatter': 'security',
        },
    },
    'loggers': {
        'security': {
            'handlers': ['security_file'],
            'level': 'WARNING',
            'propagate': False,
        },
        'auth': {
            'handlers': ['auth_file'],
            'level': 'INFO',
            'propagate': False,
        },
    }
}

# 安全事件记录器
import logging

security_logger = logging.getLogger('security')
auth_logger = logging.getLogger('auth')

class SecurityLogger:
    @staticmethod
    def log_login_attempt(user, ip, success, reason=''):
        extra = {'user': user, 'ip': ip}
        if success:
            auth_logger.info(f'Successful login for user {user}', extra=extra)
        else:
            auth_logger.warning(f'Failed login for user {user}: {reason}', extra=extra)

    @staticmethod
    def log_permission_denied(user, resource, ip):
        extra = {'user': user, 'ip': ip}
        security_logger.warning(f'Permission denied for user {user} accessing {resource}', extra=extra)

    @staticmethod
    def log_suspicious_activity(description, user, ip, data=None):
        extra = {'user': user, 'ip': ip}
        security_logger.error(f'Suspicious activity: {description}', extra=extra)</code></pre>
              </div>
            </div>
          </div>

          <div>
            <h3 class="text-xl font-bold text-indigo-800 mb-4">实时监控</h3>
            <div class="space-y-4">
              <div class="bg-purple-50 p-4 rounded-lg border border-purple-200">
                <h4 class="font-bold text-purple-700 mb-2">异常检测中间件</h4>
                <pre class="bg-white p-3 rounded text-sm overflow-x-auto"><code># 异常检测中间件
class SecurityMonitoringMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response
        self.suspicious_patterns = [
            r'&lt;script.*?&gt;',
            r'union.*select',
            r'\.\./',
            r'etc/passwd',
            r'cmd\.exe',
        ]

    def __call__(self, request):
        # 检查可疑请求
        if self.is_suspicious_request(request):
            self.handle_suspicious_request(request)

        response = self.get_response(request)

        # 检查响应异常
        if self.is_suspicious_response(response):
            self.handle_suspicious_response(request, response)

        return response

    def is_suspicious_request(self, request):
        # 检查请求参数
        all_params = {**request.GET.dict(), **request.POST.dict()}

        for param, value in all_params.items():
            for pattern in self.suspicious_patterns:
                if re.search(pattern, str(value), re.IGNORECASE):
                    return True

        # 检查User-Agent
        user_agent = request.META.get('HTTP_USER_AGENT', '')
        if 'sqlmap' in user_agent.lower() or 'nikto' in user_agent.lower():
            return True

        return False

    def is_suspicious_response(self, response):
        # 检查是否泄露敏感信息
        if hasattr(response, 'content'):
            content = response.content.decode('utf-8', errors='ignore')

            sensitive_patterns = [
                r'SECRET_KEY',
                r'password.*=',
                r'mysql.*error',
                r'traceback',
            ]

            for pattern in sensitive_patterns:
                if re.search(pattern, content, re.IGNORECASE):
                    return True

        return False

    def handle_suspicious_request(self, request):
        ip = self.get_client_ip(request)
        user = getattr(request, 'user', 'Anonymous')

        SecurityLogger.log_suspicious_activity(
            'Suspicious request detected',
            user,
            ip,
            {
                'path': request.path,
                'method': request.method,
                'params': dict(request.GET),
            }
        )

        # 可选：阻止请求
        # return HttpResponseForbidden('Suspicious activity detected')

    def get_client_ip(self, request):
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip</code></pre>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DjangoHardening'
}
</script>

<style scoped>
@keyframes blob {
  0% { transform: translate(0px, 0px) scale(1); }
  33% { transform: translate(30px, -50px) scale(1.1); }
  66% { transform: translate(-20px, 20px) scale(0.9); }
  100% { transform: translate(0px, 0px) scale(1); }
}

.animate-blob {
  animation: blob 7s infinite;
}

.animation-delay-2000 {
  animation-delay: 2s;
}
</style>
