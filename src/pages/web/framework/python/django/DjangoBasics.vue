<template>
  <div class="min-h-screen bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50 flex flex-col relative">
    <!-- 背景装饰 -->
    <div class="absolute inset-0 overflow-hidden pointer-events-none">
      <div class="absolute -top-40 -right-40 w-80 h-80 bg-green-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob"></div>
      <div class="absolute -bottom-40 -left-40 w-80 h-80 bg-emerald-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000"></div>
    </div>

    <div class="max-w-7xl mx-auto py-12 px-4 sm:px-6 md:px-8 w-full relative z-10">
      <!-- 返回按钮 -->
      <div class="mb-8">
        <router-link to="/web/framework/python/django" class="inline-flex items-center text-green-600 hover:text-green-700 transition-colors duration-200">
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
          </svg>
          返回 Django 框架
        </router-link>
      </div>

      <!-- 页面头部 -->
      <div class="text-center mb-12">
        <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-600 rounded-full mb-6 shadow-lg">
          <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C20.832 18.477 19.246 18 17.5 18c-1.746 0-3.332.477-4.5 1.253"/>
          </svg>
        </div>
        <h1 class="text-4xl font-extrabold text-transparent bg-clip-text bg-gradient-to-r from-green-600 to-emerald-600 mb-4">
          Django 基础知识
        </h1>
        <p class="text-xl text-gray-700 max-w-4xl mx-auto leading-relaxed">
          🚀 全面掌握Django框架的核心概念、架构设计和基础用法
        </p>
      </div>

      <!-- Django核心架构 -->
      <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-white/50 mb-8">
        <h2 class="text-3xl font-bold text-green-800 mb-6 flex items-center">
          <svg class="w-8 h-8 mr-3 text-green-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
          </svg>
          Django 核心架构
        </h2>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-bold text-green-800 mb-4">MVT架构模式</h3>
            <div class="space-y-4">
              <div class="bg-green-50 p-4 rounded-lg border border-green-200">
                <h4 class="font-bold text-green-700 mb-2">Model-View-Template</h4>
                <p class="text-green-600 text-sm mb-3">
                  Django采用MVT架构模式，将应用程序分为模型(Model)、视图(View)和模板(Template)三个部分，
                  实现了业务逻辑、数据处理和用户界面的分离。
                </p>
                <div class="space-y-2 text-sm">
                  <div class="bg-white p-2 rounded border">
                    <strong>Model：</strong>数据模型，定义数据结构和业务逻辑
                  </div>
                  <div class="bg-white p-2 rounded border">
                    <strong>View：</strong>视图函数，处理请求和响应逻辑
                  </div>
                  <div class="bg-white p-2 rounded border">
                    <strong>Template：</strong>模板文件，定义用户界面和展示逻辑
                  </div>
                </div>
              </div>

              <div class="bg-emerald-50 p-4 rounded-lg border border-emerald-200">
                <h4 class="font-bold text-emerald-700 mb-2">核心组件</h4>
                <div class="space-y-2 text-sm">
                  <div class="bg-white p-2 rounded border">
                    <strong>ORM：</strong>对象关系映射，数据库抽象层
                  </div>
                  <div class="bg-white p-2 rounded border">
                    <strong>URL Dispatcher：</strong>URL路由分发器
                  </div>
                  <div class="bg-white p-2 rounded border">
                    <strong>Template Engine：</strong>模板引擎
                  </div>
                  <div class="bg-white p-2 rounded border">
                    <strong>Middleware：</strong>中间件系统
                  </div>
                  <div class="bg-white p-2 rounded border">
                    <strong>Admin：</strong>自动化管理后台
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div>
            <h3 class="text-xl font-bold text-green-800 mb-4">项目结构</h3>
            <div class="bg-gray-50 p-4 rounded-lg border">
              <pre class="text-sm text-gray-700 overflow-x-auto"><code>myproject/
├── manage.py               # 项目管理脚本
├── myproject/              # 项目配置目录
│   ├── __init__.py
│   ├── settings.py         # 项目设置
│   ├── urls.py             # 根URL配置
│   ├── wsgi.py             # WSGI配置
│   └── asgi.py             # ASGI配置
├── myapp/                  # 应用目录
│   ├── __init__.py
│   ├── admin.py            # 管理后台配置
│   ├── apps.py             # 应用配置
│   ├── models.py           # 数据模型
│   ├── views.py            # 视图函数
│   ├── urls.py             # 应用URL配置
│   ├── forms.py            # 表单定义
│   ├── tests.py            # 测试文件
│   ├── migrations/         # 数据库迁移
│   │   ├── __init__.py
│   │   └── 0001_initial.py
│   ├── templates/          # 模板文件
│   │   └── myapp/
│   │       └── index.html
│   └── static/             # 静态文件
│       └── myapp/
│           ├── css/
│           ├── js/
│           └── images/
├── static/                 # 全局静态文件
├── media/                  # 用户上传文件
├── templates/              # 全局模板
├── locale/                 # 国际化文件
├── requirements.txt        # 依赖包列表
└── .env                    # 环境变量</code></pre>
            </div>
          </div>
        </div>
      </div>

      <!-- Django模型系统 -->
      <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-white/50 mb-8">
        <h2 class="text-3xl font-bold text-emerald-800 mb-6 flex items-center">
          <svg class="w-8 h-8 mr-3 text-emerald-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5c0 2.21-3.582 4-8 4s-8-1.79-8-4"/>
          </svg>
          Django 模型系统
        </h2>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-bold text-emerald-800 mb-4">模型定义</h3>
            <div class="space-y-4">
              <div class="bg-blue-50 p-3 rounded border border-blue-200">
                <strong class="text-blue-700">基础模型：</strong>
                <pre class="bg-white p-2 rounded mt-2 text-xs"><code># models.py
from django.db import models
from django.contrib.auth.models import User

class Category(models.Model):
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = "分类"
        verbose_name_plural = "分类"
        ordering = ['name']
    
    def __str__(self):
        return self.name

class Post(models.Model):
    STATUS_CHOICES = [
        ('draft', '草稿'),
        ('published', '已发布'),
        ('archived', '已归档'),
    ]
    
    title = models.CharField(max_length=200)
    slug = models.SlugField(unique=True)
    content = models.TextField()
    status = models.CharField(max_length=10, choices=STATUS_CHOICES, default='draft')
    category = models.ForeignKey(Category, on_delete=models.CASCADE)
    author = models.ForeignKey(User, on_delete=models.CASCADE)
    tags = models.ManyToManyField('Tag', blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['status', 'created_at']),
        ]
    
    def __str__(self):
        return self.title

class Tag(models.Model):
    name = models.CharField(max_length=50, unique=True)
    
    def __str__(self):
        return self.name</code></pre>
              </div>
            </div>
          </div>

          <div>
            <h3 class="text-xl font-bold text-emerald-800 mb-4">ORM查询</h3>
            <div class="space-y-4">
              <div class="bg-green-50 p-3 rounded border border-green-200">
                <strong class="text-green-700">查询操作：</strong>
                <pre class="bg-white p-2 rounded mt-2 text-xs"><code># 基础查询
posts = Post.objects.all()
published_posts = Post.objects.filter(status='published')
recent_posts = Post.objects.filter(created_at__gte=datetime.now() - timedelta(days=7))

# 复杂查询
from django.db.models import Q, Count, Avg

# Q对象查询
posts = Post.objects.filter(
    Q(title__icontains='Django') | Q(content__icontains='Django')
)

# 聚合查询
category_stats = Category.objects.annotate(
    post_count=Count('post'),
    avg_content_length=Avg('post__content__length')
)

# 关联查询
posts_with_author = Post.objects.select_related('author', 'category')
posts_with_tags = Post.objects.prefetch_related('tags')

# 原生SQL
from django.db import connection

with connection.cursor() as cursor:
    cursor.execute("SELECT * FROM myapp_post WHERE status = %s", ['published'])
    results = cursor.fetchall()

# 批量操作
Post.objects.bulk_create([
    Post(title='Post 1', content='Content 1'),
    Post(title='Post 2', content='Content 2'),
])

Post.objects.filter(status='draft').update(status='published')
Post.objects.filter(status='archived').delete()</code></pre>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Django视图系统 -->
      <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-white/50 mb-8">
        <h2 class="text-3xl font-bold text-teal-800 mb-6 flex items-center">
          <svg class="w-8 h-8 mr-3 text-teal-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
            <path stroke-linecap="round" stroke-linejoin="round" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
          </svg>
          Django 视图系统
        </h2>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-bold text-teal-800 mb-4">函数视图</h3>
            <div class="space-y-4">
              <div class="bg-teal-50 p-3 rounded border border-teal-200">
                <strong class="text-teal-700">基础视图函数：</strong>
                <pre class="bg-white p-2 rounded mt-2 text-xs"><code># views.py
from django.shortcuts import render, get_object_or_404, redirect
from django.http import HttpResponse, JsonResponse
from django.contrib.auth.decorators import login_required
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from .models import Post, Category
from .forms import PostForm

def post_list(request):
    """文章列表视图"""
    posts = Post.objects.filter(status='published').select_related('author', 'category')
    categories = Category.objects.all()
    
    context = {
        'posts': posts,
        'categories': categories,
    }
    return render(request, 'blog/post_list.html', context)

def post_detail(request, slug):
    """文章详情视图"""
    post = get_object_or_404(Post, slug=slug, status='published')
    return render(request, 'blog/post_detail.html', {'post': post})

@login_required
@require_http_methods(["GET", "POST"])
def post_create(request):
    """创建文章视图"""
    if request.method == 'POST':
        form = PostForm(request.POST)
        if form.is_valid():
            post = form.save(commit=False)
            post.author = request.user
            post.save()
            form.save_m2m()
            return redirect('post_detail', slug=post.slug)
    else:
        form = PostForm()
    
    return render(request, 'blog/post_form.html', {'form': form})

@csrf_exempt
def api_posts(request):
    """API视图"""
    if request.method == 'GET':
        posts = Post.objects.filter(status='published').values(
            'id', 'title', 'slug', 'created_at'
        )
        return JsonResponse(list(posts), safe=False)
    
    return JsonResponse({'error': 'Method not allowed'}, status=405)</code></pre>
              </div>
            </div>
          </div>

          <div>
            <h3 class="text-xl font-bold text-teal-800 mb-4">类视图</h3>
            <div class="space-y-4">
              <div class="bg-blue-50 p-3 rounded border border-blue-200">
                <strong class="text-blue-700">基于类的视图：</strong>
                <pre class="bg-white p-2 rounded mt-2 text-xs"><code># views.py
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.urls import reverse_lazy

class PostListView(ListView):
    """文章列表视图"""
    model = Post
    template_name = 'blog/post_list.html'
    context_object_name = 'posts'
    paginate_by = 10
    
    def get_queryset(self):
        return Post.objects.filter(status='published').select_related('author', 'category')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['categories'] = Category.objects.all()
        return context

class PostDetailView(DetailView):
    """文章详情视图"""
    model = Post
    template_name = 'blog/post_detail.html'
    context_object_name = 'post'
    slug_field = 'slug'
    
    def get_queryset(self):
        return Post.objects.filter(status='published')

class PostCreateView(LoginRequiredMixin, CreateView):
    """创建文章视图"""
    model = Post
    form_class = PostForm
    template_name = 'blog/post_form.html'
    
    def form_valid(self, form):
        form.instance.author = self.request.user
        return super().form_valid(form)

class PostUpdateView(LoginRequiredMixin, UpdateView):
    """更新文章视图"""
    model = Post
    form_class = PostForm
    template_name = 'blog/post_form.html'
    
    def get_queryset(self):
        return Post.objects.filter(author=self.request.user)

class PostDeleteView(LoginRequiredMixin, DeleteView):
    """删除文章视图"""
    model = Post
    template_name = 'blog/post_confirm_delete.html'
    success_url = reverse_lazy('post_list')
    
    def get_queryset(self):
        return Post.objects.filter(author=self.request.user)</code></pre>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Django模板系统 -->
      <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-white/50 mb-8">
        <h2 class="text-3xl font-bold text-cyan-800 mb-6 flex items-center">
          <svg class="w-8 h-8 mr-3 text-cyan-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01"/>
          </svg>
          Django 模板系统
        </h2>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-bold text-cyan-800 mb-4">模板语法</h3>
            <div class="space-y-4">
              <div class="bg-cyan-50 p-3 rounded border border-cyan-200">
                <strong class="text-cyan-700">基础模板：</strong>
                <pre class="bg-white p-2 rounded mt-2 text-xs"><code>&lt;!-- templates/base.html --&gt;
&lt;!DOCTYPE html&gt;
&lt;html lang="zh-cn"&gt;
&lt;head&gt;
    &lt;meta charset="UTF-8"&gt;
    &lt;meta name="viewport" content="width=device-width, initial-scale=1.0"&gt;
    &lt;title&gt;&#123;% block title %%}#125;我的博客&#123;% endblock %%}#125;&lt;/title&gt;
    &#123;% load static %%}#125;
    &lt;link rel="stylesheet" href="&#123;% static 'css/style.css' %%}#125;"&gt;
    &#123;% block extra_css %%}#125;&#123;% endblock %%}#125;
&lt;/head&gt;
&lt;body&gt;
    &lt;header&gt;
        &lt;nav&gt;
            &lt;a href="&#123;% url 'post_list' %%}#125;"&gt;首页&lt;/a&gt;
            &#123;% if user.is_authenticated %%}#125;
                &lt;a href="&#123;% url 'post_create' %%}#125;"&gt;写文章&lt;/a&gt;
                &lt;a href="&#123;% url 'logout' %&#125;"&gt;退出 (&#123;&#123; user.username &#125;&#125;)&lt;/a&gt;
            &#123;% else %%}#125;
                &lt;a href="&#123;% url 'login' %%}#125;"&gt;登录&lt;/a&gt;
            &#123;% endif %%}#125;
        &lt;/nav&gt;
    &lt;/header&gt;

    &lt;main&gt;
        &#123;% if messages %%}#125;
            &#123;% for message in messages %%}#125;
                &lt;div class="alert alert-&#123;&#123; message.tags &#125;&#125;"&gt;
                    &#123;&#123; message &#125;&#125;
                &lt;/div&gt;
            &#123;% endfor %%}#125;
        &#123;% endif %%}#125;

        &#123;% block content %%}#125;
        &#123;% endblock %%}#125;
    &lt;/main&gt;

    &lt;footer&gt;
        &lt;p&gt;&amp;copy; &#123;% now "Y" %%}#125; 我的博客. All rights reserved.&lt;/p&gt;
    &lt;/footer&gt;

    &#123;% block extra_js %%}#125;&#123;% endblock %%}#125;
&lt;/body&gt;
&lt;/html&gt;</code></pre>
              </div>

              <div class="bg-blue-50 p-3 rounded border border-blue-200">
                <strong class="text-blue-700">子模板：</strong>
                <pre class="bg-white p-2 rounded mt-2 text-xs"><code>&lt;!-- templates/blog/post_list.html --&gt;
&#123;% extends 'base.html' %%}#125;
&#123;% load static %%}#125;

&#123;% block title %&#125;文章列表 - &#123;&#123; block.super &#125;&#125;&#123;% endblock %&#125;

&#123;% block content %%}#125;
&lt;div class="container"&gt;
    &lt;h1&gt;最新文章&lt;/h1&gt;

    &lt;div class="sidebar"&gt;
        &lt;h3&gt;分类&lt;/h3&gt;
        &lt;ul&gt;
            &#123;% for category in categories %%}#125;
                &lt;li&gt;&lt;a href="?category=&#123;&#123; category.id &#125;&#125;"&gt;&#123;&#123; category.name &#125;&#125;&lt;/a&gt;&lt;/li&gt;
            &#123;% empty %%}#125;
                &lt;li&gt;暂无分类&lt;/li&gt;
            &#123;% endfor %%}#125;
        &lt;/ul&gt;
    &lt;/div&gt;

    &lt;div class="posts"&gt;
        &#123;% for post in posts %%}#125;
            &lt;article class="post"&gt;
                &lt;h2&gt;&lt;a href="&#123;% url 'post_detail' post.slug %&#125;"&gt;&#123;&#123; post.title &#125;&#125;&lt;/a&gt;&lt;/h2&gt;
                &lt;div class="meta"&gt;
                    &lt;span&gt;作者：&#123;&#123; post.author.username &#125;&#125;&lt;/span&gt;
                    &lt;span&gt;分类：&#123;&#123; post.category.name &#125;&#125;&lt;/span&gt;
                    &lt;span&gt;时间：&#123;&#123; post.created_at|date:"Y-m-d H:i" &#125;&#125;&lt;/span&gt;
                &lt;/div&gt;
                &lt;div class="content"&gt;
                    &#123;&#123; post.content|truncatewords:50|linebreaks &#125;&#125;
                &lt;/div&gt;
                &lt;div class="tags"&gt;
                    &#123;% for tag in post.tags.all %%}#125;
                        &lt;span class="tag"&gt;&#123;&#123; tag.name &#125;&#125;&lt;/span&gt;
                    &#123;% endfor %%}#125;
                &lt;/div&gt;
            &lt;/article&gt;
        &#123;% empty %%}#125;
            &lt;p&gt;暂无文章&lt;/p&gt;
        &#123;% endfor %%}#125;

        &#123;% if is_paginated %%}#125;
            &lt;div class="pagination"&gt;
                &#123;% if page_obj.has_previous %%}#125;
                    &lt;a href="?page=1"&gt;首页&lt;/a&gt;
                    &lt;a href="?page=&#123;&#123; page_obj.previous_page_number &#125;&#125;"&gt;上一页&lt;/a&gt;
                &#123;% endif %%}#125;

                &lt;span&gt;第 &#123;&#123; page_obj.number &#125;&#125; 页，共 &#123;&#123; page_obj.paginator.num_pages &#125;&#125; 页&lt;/span&gt;

                &#123;% if page_obj.has_next %%}#125;
                    &lt;a href="?page=&#123;&#123; page_obj.next_page_number &#125;&#125;"&gt;下一页&lt;/a&gt;
                    &lt;a href="?page=&#123;&#123; page_obj.paginator.num_pages &#125;&#125;"&gt;末页&lt;/a&gt;
                &#123;% endif %%}#125;
            &lt;/div&gt;
        &#123;% endif %%}#125;
    &lt;/div&gt;
&lt;/div&gt;
&#123;% endblock %%}#125;</code></pre>
              </div>
            </div>
          </div>

          <div>
            <h3 class="text-xl font-bold text-cyan-800 mb-4">模板标签和过滤器</h3>
            <div class="space-y-4">
              <div class="bg-green-50 p-3 rounded border border-green-200">
                <strong class="text-green-700">自定义标签：</strong>
                <pre class="bg-white p-2 rounded mt-2 text-xs"><code># templatetags/blog_tags.py
from django import template
from django.utils.safestring import mark_safe
from django.db.models import Count
from ..models import Post, Category

register = template.Library()

@register.simple_tag
def total_posts():
    """获取文章总数"""
    return Post.objects.filter(status='published').count()

@register.inclusion_tag('blog/latest_posts.html')
def show_latest_posts(count=5):
    """显示最新文章"""
    latest_posts = Post.objects.filter(status='published')[:count]
    return {'latest_posts': latest_posts}

@register.filter
def markdown(value):
    """Markdown过滤器"""
    import markdown
    return mark_safe(markdown.markdown(value))

@register.assignment_tag
def get_categories():
    """获取所有分类"""
    return Category.objects.annotate(post_count=Count('post'))

# 使用示例
# &#123;% load blog_tags %%}#125;
# &#123;% total_posts %%}#125; 篇文章
# &#123;% show_latest_posts 3 %%}#125;
# &#123;&#123; post.content|markdown &#125;&#125;
# &#123;% get_categories as categories %%}#125;</code></pre>
              </div>

              <div class="bg-yellow-50 p-3 rounded border border-yellow-200">
                <strong class="text-yellow-700">内置过滤器：</strong>
                <pre class="bg-white p-2 rounded mt-2 text-xs"><code>&lt;!-- 常用过滤器示例 --&gt;

&lt;!-- 日期格式化 --&gt;
&#123;&#123; post.created_at|date:"Y-m-d H:i:s" &#125;&#125;
&#123;&#123; post.created_at|timesince &#125;&#125; 前

&lt;!-- 字符串处理 --&gt;
&#123;&#123; post.title|upper &#125;&#125;
&#123;&#123; post.content|truncatewords:20 &#125;&#125;
&#123;&#123; post.content|truncatechars:100 &#125;&#125;
&#123;&#123; post.content|linebreaks &#125;&#125;
&#123;&#123; post.content|striptags &#125;&#125;

&lt;!-- 数字处理 --&gt;
&#123;&#123; price|floatformat:2 &#125;&#125;
&#123;&#123; number|add:10 &#125;&#125;

&lt;!-- 列表处理 --&gt;
&#123;&#123; tags|join:", " &#125;&#125;
&#123;&#123; posts|length &#125;&#125;
&#123;&#123; posts|first &#125;&#125;
&#123;&#123; posts|last &#125;&#125;

&lt;!-- 条件判断 --&gt;
&#123;&#123; value|default:"暂无数据" &#125;&#125;
&#123;&#123; value|default_if_none:"N/A" &#125;&#125;
&#123;&#123; value|yesno:"是,否,未知" &#125;&#125;

&lt;!-- URL处理 --&gt;
&#123;&#123; url|urlencode &#125;&#125;
&#123;&#123; text|urlize &#125;&#125;

&lt;!-- 安全处理 --&gt;
&#123;&#123; html_content|safe &#125;&#125;
&#123;&#123; user_input|escape &#125;&#125;</code></pre>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Django URL路由 -->
      <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-white/50 mb-8">
        <h2 class="text-3xl font-bold text-indigo-800 mb-6 flex items-center">
          <svg class="w-8 h-8 mr-3 text-indigo-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"/>
          </svg>
          Django URL路由
        </h2>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-bold text-indigo-800 mb-4">URL配置</h3>
            <div class="space-y-4">
              <div class="bg-indigo-50 p-3 rounded border border-indigo-200">
                <strong class="text-indigo-700">项目URL配置：</strong>
                <pre class="bg-white p-2 rounded mt-2 text-xs"><code># myproject/urls.py
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static

urlpatterns = [
    path('admin/', admin.site.urls),
    path('', include('blog.urls')),
    path('accounts/', include('django.contrib.auth.urls')),
    path('api/', include('api.urls')),
]

# 开发环境静态文件服务
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)</code></pre>
              </div>

              <div class="bg-blue-50 p-3 rounded border border-blue-200">
                <strong class="text-blue-700">应用URL配置：</strong>
                <pre class="bg-white p-2 rounded mt-2 text-xs"><code># blog/urls.py
from django.urls import path
from . import views

app_name = 'blog'

urlpatterns = [
    # 函数视图
    path('', views.post_list, name='post_list'),
    path('post/&lt;slug:slug&gt;/', views.post_detail, name='post_detail'),
    path('create/', views.post_create, name='post_create'),

    # 类视图
    path('posts/', views.PostListView.as_view(), name='post_list_cbv'),
    path('post/&lt;slug:slug&gt;/edit/', views.PostUpdateView.as_view(), name='post_edit'),
    path('post/&lt;slug:slug&gt;/delete/', views.PostDeleteView.as_view(), name='post_delete'),

    # 带参数的URL
    path('category/&lt;int:category_id&gt;/', views.category_posts, name='category_posts'),
    path('tag/&lt;str:tag_name&gt;/', views.tag_posts, name='tag_posts'),
    path('archive/&lt;int:year&gt;/&lt;int:month&gt;/', views.archive_posts, name='archive_posts'),

    # 正则表达式URL
    re_path(r'^search/$', views.search_posts, name='search'),
    re_path(r'^feed/$', views.PostFeed(), name='post_feed'),
]</code></pre>
              </div>
            </div>
          </div>

          <div>
            <h3 class="text-xl font-bold text-indigo-800 mb-4">URL反向解析</h3>
            <div class="space-y-4">
              <div class="bg-purple-50 p-3 rounded border border-purple-200">
                <strong class="text-purple-700">在视图中使用：</strong>
                <pre class="bg-white p-2 rounded mt-2 text-xs"><code># views.py
from django.urls import reverse, reverse_lazy
from django.shortcuts import redirect

def post_create(request):
    if request.method == 'POST':
        # 处理表单
        post = form.save()
        # 重定向到文章详情页
        return redirect('blog:post_detail', slug=post.slug)
        # 或者使用reverse
        # return redirect(reverse('blog:post_detail', kwargs={'slug': post.slug}))

    return render(request, 'blog/post_form.html', {'form': form})

# 在类视图中使用
class PostCreateView(CreateView):
    model = Post
    form_class = PostForm
    success_url = reverse_lazy('blog:post_list')

    def get_success_url(self):
        return reverse('blog:post_detail', kwargs={'slug': self.object.slug})</code></pre>
              </div>

              <div class="bg-green-50 p-3 rounded border border-green-200">
                <strong class="text-green-700">在模板中使用：</strong>
                <pre class="bg-white p-2 rounded mt-2 text-xs"><code>&lt;!-- 基础URL反向解析 --&gt;
&lt;a href="&#123;% url 'blog:post_list' %%}#125;"&gt;文章列表&lt;/a&gt;
&lt;a href="&#123;% url 'blog:post_detail' post.slug %%}#125;"&gt;&#123;&#123; post.title &#125;&#125;&lt;/a&gt;

&lt;!-- 带参数的URL --&gt;
&lt;a href="&#123;% url 'blog:category_posts' category.id %%}#125;"&gt;&#123;&#123; category.name &#125;&#125;&lt;/a&gt;
&lt;a href="&#123;% url 'blog:archive_posts' year=2023 month=12 %%}#125;"&gt;2023年12月&lt;/a&gt;

&lt;!-- 表单action --&gt;
&lt;form method="post" action="&#123;% url 'blog:post_create' %%}#125;"&gt;
    &#123;% csrf_token %%}#125;
    &#123;&#123; form.as_p &#125;&#125;
    &lt;button type="submit"&gt;提交&lt;/button&gt;
&lt;/form&gt;

&lt;!-- 条件URL --&gt;
&#123;% if user.is_authenticated %%}#125;
    &lt;a href="&#123;% url 'blog:post_create' %%}#125;"&gt;写文章&lt;/a&gt;
&#123;% else %%}#125;
    &lt;a href="&#123;% url 'login' %%}#125;"&gt;登录后写文章&lt;/a&gt;
&#123;% endif %%}#125;</code></pre>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Django表单系统 -->
      <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-white/50 mb-8">
        <h2 class="text-3xl font-bold text-purple-800 mb-6 flex items-center">
          <svg class="w-8 h-8 mr-3 text-purple-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
          </svg>
          Django 表单系统
        </h2>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-bold text-purple-800 mb-4">表单定义</h3>
            <div class="space-y-4">
              <div class="bg-purple-50 p-3 rounded border border-purple-200">
                <strong class="text-purple-700">ModelForm：</strong>
                <pre class="bg-white p-2 rounded mt-2 text-xs"><code># forms.py
from django import forms
from django.contrib.auth.models import User
from .models import Post, Category

class PostForm(forms.ModelForm):
    class Meta:
        model = Post
        fields = ['title', 'content', 'category', 'tags', 'status']
        widgets = {
            'title': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '请输入文章标题'
            }),
            'content': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 10,
                'placeholder': '请输入文章内容'
            }),
            'category': forms.Select(attrs={'class': 'form-control'}),
            'tags': forms.CheckboxSelectMultiple(),
            'status': forms.Select(attrs={'class': 'form-control'}),
        }
        labels = {
            'title': '标题',
            'content': '内容',
            'category': '分类',
            'tags': '标签',
            'status': '状态',
        }

    def clean_title(self):
        title = self.cleaned_data['title']
        if len(title) < 5:
            raise forms.ValidationError('标题至少需要5个字符')
        return title

    def clean(self):
        cleaned_data = super().clean()
        title = cleaned_data.get('title')
        content = cleaned_data.get('content')

        if title and content and title in content:
            raise forms.ValidationError('标题不能包含在内容中')

        return cleaned_data</code></pre>
              </div>

              <div class="bg-blue-50 p-3 rounded border border-blue-200">
                <strong class="text-blue-700">普通表单：</strong>
                <pre class="bg-white p-2 rounded mt-2 text-xs"><code># forms.py
class ContactForm(forms.Form):
    name = forms.CharField(
        max_length=100,
        label='姓名',
        widget=forms.TextInput(attrs={'class': 'form-control'})
    )
    email = forms.EmailField(
        label='邮箱',
        widget=forms.EmailInput(attrs={'class': 'form-control'})
    )
    subject = forms.CharField(
        max_length=200,
        label='主题',
        widget=forms.TextInput(attrs={'class': 'form-control'})
    )
    message = forms.CharField(
        label='消息',
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 5
        })
    )

    def send_email(self):
        # 发送邮件逻辑
        pass

class SearchForm(forms.Form):
    query = forms.CharField(
        max_length=200,
        label='搜索关键词',
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': '请输入搜索关键词'
        })
    )
    category = forms.ModelChoiceField(
        queryset=Category.objects.all(),
        empty_label='所有分类',
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )</code></pre>
              </div>
            </div>
          </div>

          <div>
            <h3 class="text-xl font-bold text-purple-800 mb-4">表单处理</h3>
            <div class="space-y-4">
              <div class="bg-green-50 p-3 rounded border border-green-200">
                <strong class="text-green-700">视图中处理表单：</strong>
                <pre class="bg-white p-2 rounded mt-2 text-xs"><code># views.py
from django.shortcuts import render, redirect
from django.contrib import messages
from .forms import PostForm, ContactForm

def post_create(request):
    if request.method == 'POST':
        form = PostForm(request.POST, request.FILES)
        if form.is_valid():
            post = form.save(commit=False)
            post.author = request.user
            post.save()
            form.save_m2m()  # 保存多对多关系
            messages.success(request, '文章创建成功！')
            return redirect('blog:post_detail', slug=post.slug)
        else:
            messages.error(request, '表单验证失败，请检查输入')
    else:
        form = PostForm()

    return render(request, 'blog/post_form.html', {'form': form})

def contact(request):
    if request.method == 'POST':
        form = ContactForm(request.POST)
        if form.is_valid():
            # 处理表单数据
            name = form.cleaned_data['name']
            email = form.cleaned_data['email']
            subject = form.cleaned_data['subject']
            message = form.cleaned_data['message']

            # 发送邮件
            form.send_email()

            messages.success(request, '消息发送成功！')
            return redirect('contact')
    else:
        form = ContactForm()

    return render(request, 'contact.html', {'form': form})</code></pre>
              </div>

              <div class="bg-yellow-50 p-3 rounded border border-yellow-200">
                <strong class="text-yellow-700">模板中渲染表单：</strong>
                <pre class="bg-white p-2 rounded mt-2 text-xs"><code>&lt;!-- templates/blog/post_form.html --&gt;
&#123;% extends 'base.html' %%}#125;

&#123;% block content %%}#125;
&lt;div class="container"&gt;
    &lt;h1&gt;&#123;% if form.instance.pk %%}#125;编辑&#123;% else %%}#125;创建&#123;% endif %%}#125;文章&lt;/h1&gt;

    &lt;form method="post" enctype="multipart/form-data"&gt;
        &#123;% csrf_token %%}#125;

        &#123;% if form.non_field_errors %%}#125;
            &lt;div class="alert alert-danger"&gt;
                &#123;&#123; form.non_field_errors &#125;&#125;
            &lt;/div&gt;
        &#123;% endif %%}#125;

        &#123;% for field in form %%}#125;
            &lt;div class="form-group"&gt;
                &#123;&#123; field.label_tag &#125;&#125;
                &#123;&#123; field &#125;&#125;
                &#123;% if field.errors %%}#125;
                    &lt;div class="text-danger"&gt;
                        &#123;&#123; field.errors &#125;&#125;
                    &lt;/div&gt;
                &#123;% endif %%}#125;
                &#123;% if field.help_text %%}#125;
                    &lt;small class="form-text text-muted"&gt;
                        &#123;&#123; field.help_text &#125;&#125;
                    &lt;/small&gt;
                &#123;% endif %%}#125;
            &lt;/div&gt;
        &#123;% endfor %%}#125;

        &lt;button type="submit" class="btn btn-primary"&gt;保存&lt;/button&gt;
        &lt;a href="&#123;% url 'blog:post_list' %%}#125;" class="btn btn-secondary"&gt;取消&lt;/a&gt;
    &lt;/form&gt;
&lt;/div&gt;
&#123;% endblock %%}#125;</code></pre>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DjangoBasics'
}
</script>

<style scoped>
@keyframes blob {
  0% { transform: translate(0px, 0px) scale(1); }
  33% { transform: translate(30px, -50px) scale(1.1); }
  66% { transform: translate(-20px, 20px) scale(0.9); }
  100% { transform: translate(0px, 0px) scale(1); }
}

.animate-blob {
  animation: blob 7s infinite;
}

.animation-delay-2000 {
  animation-delay: 2s;
}
</style>
