<template>
  <div class="min-h-screen bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50 flex flex-col relative">
    <!-- 背景装饰 -->
    <div class="absolute inset-0 overflow-hidden pointer-events-none">
      <div class="absolute -top-40 -right-40 w-80 h-80 bg-green-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob"></div>
      <div class="absolute -bottom-40 -left-40 w-80 h-80 bg-emerald-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000"></div>
      <div class="absolute -top-40 left-1/2 w-80 h-80 bg-teal-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-4000"></div>
    </div>

    <div class="max-w-7xl mx-auto py-12 px-4 sm:px-6 md:px-8 w-full relative z-10">
      <!-- 返回按钮 -->
      <div class="mb-8">
        <router-link to="/web/framework/python" class="inline-flex items-center text-green-600 hover:text-green-700 transition-colors duration-200">
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
          </svg>
          返回 Python 框架
        </router-link>
      </div>

      <!-- 页面头部 -->
      <div class="text-center mb-12">
        <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-600 rounded-full mb-6 shadow-lg">
          <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
          </svg>
        </div>
        <h1 class="text-4xl font-extrabold text-transparent bg-clip-text bg-gradient-to-r from-green-600 to-emerald-600 mb-4">
          Django 框架
        </h1>
        <p class="text-xl text-gray-700 max-w-4xl mx-auto leading-relaxed">
          🚀 深入掌握Django框架的完整知识体系 - 从基础概念到安全防护的全方位学习指南
        </p>
        <div class="flex flex-wrap justify-center gap-3 text-sm">
          <span class="bg-green-100 text-green-700 px-3 py-1 rounded-full font-medium">MVT架构</span>
          <span class="bg-emerald-100 text-emerald-700 px-3 py-1 rounded-full font-medium">ORM系统</span>
          <span class="bg-teal-100 text-teal-700 px-3 py-1 rounded-full font-medium">Admin后台</span>
          <span class="bg-cyan-100 text-cyan-700 px-3 py-1 rounded-full font-medium">中间件</span>
          <span class="bg-green-100 text-green-700 px-3 py-1 rounded-full font-medium">安全防护</span>
          <span class="bg-emerald-100 text-emerald-700 px-3 py-1 rounded-full font-medium">配置管理</span>
        </div>
      </div>

      <!-- 学习模块 -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16">
        <!-- Django基础知识 -->
        <router-link to="/web/framework/python/django/basics" class="group">
          <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/50 hover:shadow-2xl transition-all duration-300 group-hover:scale-105 h-64 flex flex-col">
            <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center mb-4 group-hover:from-green-600 group-hover:to-green-700 transition-all duration-300">
              <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C20.832 18.477 19.246 18 17.5 18c-1.746 0-3.332.477-4.5 1.253"/>
              </svg>
            </div>
            <h3 class="text-xl font-bold text-gray-800 mb-2">Django 基础知识</h3>
            <p class="text-gray-600 text-sm flex-1">
              Django核心概念、MVT架构、ORM系统、视图函数、模板引擎、URL路由等基础知识
            </p>
          </div>
        </router-link>

        <!-- Django配置文件 -->
        <router-link to="/web/framework/python/django/config" class="group">
          <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/50 hover:shadow-2xl transition-all duration-300 group-hover:scale-105 h-64 flex flex-col">
            <div class="w-16 h-16 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-xl flex items-center justify-center mb-4 group-hover:from-emerald-600 group-hover:to-emerald-700 transition-all duration-300">
              <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
                <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
              </svg>
            </div>
            <h3 class="text-xl font-bold text-gray-800 mb-2">Django 配置文件</h3>
            <p class="text-gray-600 text-sm flex-1">
              settings.py、urls.py、wsgi.py等配置文件的详细解析和字段含义
            </p>
          </div>
        </router-link>

        <!-- Django漏洞分析 -->
        <router-link to="/web/framework/python/django/vulnerabilities" class="group">
          <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/50 hover:shadow-2xl transition-all duration-300 group-hover:scale-105 h-64 flex flex-col">
            <div class="w-16 h-16 bg-gradient-to-br from-teal-500 to-teal-600 rounded-xl flex items-center justify-center mb-4 group-hover:from-teal-600 group-hover:to-teal-700 transition-all duration-300">
              <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"/>
              </svg>
            </div>
            <h3 class="text-xl font-bold text-gray-800 mb-2">Django 漏洞分析</h3>
            <p class="text-gray-600 text-sm flex-1">
              模板注入、SQL注入、反序列化、文件包含等Django历史漏洞的分析与利用
            </p>
          </div>
        </router-link>

        <!-- Django安全加固 -->
        <router-link to="/web/framework/python/django/hardening" class="group">
          <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/50 hover:shadow-2xl transition-all duration-300 group-hover:scale-105 h-64 flex flex-col">
            <div class="w-16 h-16 bg-gradient-to-br from-cyan-500 to-cyan-600 rounded-xl flex items-center justify-center mb-4 group-hover:from-cyan-600 group-hover:to-cyan-700 transition-all duration-300">
              <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"/>
              </svg>
            </div>
            <h3 class="text-xl font-bold text-gray-800 mb-2">Django 安全加固</h3>
            <p class="text-gray-600 text-sm flex-1">
              Django应用的安全配置、中间件防护、权限控制、防护措施等安全加固方案
            </p>
          </div>
        </router-link>
      </div>

      <!-- 快速导航 -->
      <div class="bg-white/60 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-white/50">
        <h2 class="text-2xl font-bold text-gray-800 mb-6 text-center">Django 学习路径</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div class="text-center">
            <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
              <span class="text-green-600 font-bold">1</span>
            </div>
            <h3 class="font-semibold text-gray-800 mb-2">基础学习</h3>
            <p class="text-sm text-gray-600">掌握Django核心概念和基础用法</p>
          </div>
          <div class="text-center">
            <div class="w-12 h-12 bg-emerald-100 rounded-full flex items-center justify-center mx-auto mb-3">
              <span class="text-emerald-600 font-bold">2</span>
            </div>
            <h3 class="font-semibold text-gray-800 mb-2">配置详解</h3>
            <p class="text-sm text-gray-600">深入理解配置文件结构和参数含义</p>
          </div>
          <div class="text-center">
            <div class="w-12 h-12 bg-teal-100 rounded-full flex items-center justify-center mx-auto mb-3">
              <span class="text-teal-600 font-bold">3</span>
            </div>
            <h3 class="font-semibold text-gray-800 mb-2">漏洞研究</h3>
            <p class="text-sm text-gray-600">分析历史漏洞和攻击技术</p>
          </div>
          <div class="text-center">
            <div class="w-12 h-12 bg-cyan-100 rounded-full flex items-center justify-center mx-auto mb-3">
              <span class="text-cyan-600 font-bold">4</span>
            </div>
            <h3 class="font-semibold text-gray-800 mb-2">安全防护</h3>
            <p class="text-sm text-gray-600">实施全面的安全加固措施</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DjangoFramework'
}
</script>

<style scoped>
@keyframes blob {
  0% { transform: translate(0px, 0px) scale(1); }
  33% { transform: translate(30px, -50px) scale(1.1); }
  66% { transform: translate(-20px, 20px) scale(0.9); }
  100% { transform: translate(0px, 0px) scale(1); }
}

.animate-blob {
  animation: blob 7s infinite;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-4000 {
  animation-delay: 4s;
}
</style>
