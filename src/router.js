import { createRouter, createWebHistory } from 'vue-router'
import CTFHome from './pages/CTFHome.vue'

import Home from './pages/Home.vue'

const Web = () => import('./pages/Web.vue')
const Middleware = () => import('./pages/web/middleware/Middleware.vue')
const NginxCompleteConfig = () => import('./pages/web/middleware/nginx/CompleteConfig.vue')
const NginxVulnerabilitiesDatabase = () => import('./pages/web/middleware/nginx/VulnerabilitiesDatabase.vue')
const NginxSecurityHardening = () => import('./pages/web/middleware/nginx/SecurityHardening.vue')
const NginxpwnerManual = () => import('./pages/web/middleware/nginx/NginxpwnerManual.vue')

// Apache pages
const ApacheCompleteConfig = () => import('./pages/web/middleware/apache/CompleteConfig.vue')
const ApacheVulnerabilitiesDatabase = () => import('./pages/web/middleware/apache/VulnerabilitiesDatabase.vue')
const ApacheSecurityHardening = () => import('./pages/web/middleware/apache/SecurityHardening.vue')

// IIS pages
const IISCompleteConfig = () => import('./pages/web/middleware/iis/CompleteConfig.vue')
const IISVulnerabilitiesDatabase = () => import('./pages/web/middleware/iis/VulnerabilitiesDatabase.vue')
const IISSecurityHardening = () => import('./pages/web/middleware/iis/SecurityHardening.vue')

// Tomcat pages
const TomcatCompleteConfig = () => import('./pages/web/middleware/tomcat/CompleteConfig.vue')
const TomcatVulnerabilitiesDatabase = () => import('./pages/web/middleware/tomcat/VulnerabilitiesDatabase.vue')
const TomcatSecurityHardening = () => import('./pages/web/middleware/tomcat/SecurityHardening.vue')
const TomcatSecurityHardeningMain = () => import('./pages/web/middleware/tomcat/SecurityHardeningMain.vue')

// JBoss pages
const JBossCompleteConfig = () => import('./pages/web/middleware/jboss/CompleteConfig.vue')
const JBossVulnerabilitiesDatabase = () => import('./pages/web/middleware/jboss/VulnerabilitiesDatabase.vue')
const JBossSecurityHardening = () => import('./pages/web/middleware/jboss/SecurityHardening.vue')

// WebLogic pages
const WebLogicCompleteConfig = () => import('./pages/web/middleware/weblogic/CompleteConfig.vue')
const WebLogicVulnerabilitiesDatabase = () => import('./pages/web/middleware/weblogic/VulnerabilitiesDatabase.vue')
const WebLogicSecurityHardening = () => import('./pages/web/middleware/weblogic/SecurityHardening.vue')
const JavaBase = () => import('./pages/web/java/JavaBase.vue')
const Reflection = () => import('./pages/web/java/Reflection.vue')
const ReflectionVuln = () => import('./pages/web/java/ReflectionVuln.vue')
const Proxy = () => import('./pages/web/java/Proxy.vue')
const ClassLoader = () => import('./pages/web/java/ClassLoader.vue')
const Serialization = () => import('./pages/web/java/Serialization.vue')
const DeserializationRisk = () => import('./pages/web/java/DeserializationRisk.vue')
const URLDNS = () => import('./pages/web/java/chain/urldns.vue')
const JDK7u21 = () => import('./pages/web/java/chain/jdk7u21.vue')
const JDK8u20 = () => import('./pages/web/java/chain/jdk8u20.vue')
const JRMPClient = () => import('./pages/web/java/chain/jrmpclient.vue')
const JRMPListener = () => import('./pages/web/java/chain/jrmplistener.vue')
const Spring1 = () => import('./pages/web/java/chain/spring1.vue')
const Spring2 = () => import('./pages/web/java/chain/spring2.vue')
const Spring3 = () => import('./pages/web/java/chain/spring3.vue')
const SpringBoot = () => import('./pages/web/java/chain/springboot.vue')
const SpringCloud = () => import('./pages/web/java/chain/springcloud.vue')
const SpringSecurity = () => import('./pages/web/java/chain/springsecurity.vue')
const CC1 = () => import('./pages/web/java/chain/cc1.vue')
const CC2 = () => import('./pages/web/java/chain/cc2.vue')
const CC3 = () => import('./pages/web/java/chain/cc3.vue')
const CC4 = () => import('./pages/web/java/chain/cc4.vue')
const CC5 = () => import('./pages/web/java/chain/cc5.vue')
const CC6 = () => import('./pages/web/java/chain/cc6.vue')
const CC7 = () => import('./pages/web/java/chain/cc7.vue')
const CC11 = () => import('./pages/web/java/chain/cc11.vue')
const JavaTools = () => import('./pages/web/java/JavaTools.vue')
const YsoserialTool = () => import('./pages/web/java/tools/ysoserial.vue')
const JNDIExploitTool = () => import('./pages/web/java/tools/jndiexploit.vue')
const MarshalsecTool = () => import('./pages/web/java/tools/marshalsec.vue')
const MemShellTool = () => import('./pages/web/java/tools/memshell.vue')
const JysoTool = () => import('./pages/web/java/tools/jyso.vue')

// Eclipse Jetty middleware pages
const JettyCompleteConfig = () => import('./pages/web/middleware/jetty/CompleteConfig.vue')
const JettyVulnerabilitiesMain = () => import('./pages/web/middleware/jetty/VulnerabilitiesMain.vue')
const JettySecurityHardeningMain = () => import('./pages/web/middleware/jetty/SecurityHardeningMain.vue')

// MySQL Proxy middleware pages
const MySQLProxyCompleteConfig = () => import('./pages/web/middleware/mysql-proxy/CompleteConfig.vue')
const MySQLProxyVulnerabilitiesMain = () => import('./pages/web/middleware/mysql-proxy/VulnerabilitiesMain.vue')
const MySQLProxySecurityHardeningMain = () => import('./pages/web/middleware/mysql-proxy/SecurityHardeningMain.vue')

// MyCat middleware pages
const MyCatConfig = () => import('./pages/web/middleware/mycat/MyCatConfig.vue')
const MyCatVulnerabilities = () => import('./pages/web/middleware/mycat/MyCatVulnerabilities.vue')
const MyCatHardening = () => import('./pages/web/middleware/mycat/MyCatHardening.vue')

// RabbitMQ middleware pages
const RabbitMQConfig = () => import('./pages/web/middleware/rabbitmq/RabbitMQConfig.vue')
const RabbitMQVulnerabilities = () => import('./pages/web/middleware/rabbitmq/RabbitMQVulnerabilities.vue')
const RabbitMQHardening = () => import('./pages/web/middleware/rabbitmq/RabbitMQHardening.vue')

// ActiveMQ middleware pages
const ActiveMQConfig = () => import('./pages/web/middleware/activemq/ActiveMQConfig.vue')
const ActiveMQVulnerabilities = () => import('./pages/web/middleware/activemq/ActiveMQVulnerabilities.vue')
const ActiveMQHardening = () => import('./pages/web/middleware/activemq/ActiveMQHardening.vue')

// Kafka middleware pages
const KafkaConfig = () => import('./pages/web/middleware/kafka/KafkaConfig.vue')
const KafkaVulnerabilities = () => import('./pages/web/middleware/kafka/KafkaVulnerabilities.vue')
const KafkaHardening = () => import('./pages/web/middleware/kafka/KafkaHardening.vue')

// Redis middleware pages
const RedisConfig = () => import('./pages/web/middleware/redis/RedisConfig.vue')
const RedisVulnerabilities = () => import('./pages/web/middleware/redis/RedisVulnerabilities.vue')
const RedisExploitationTools = () => import('./pages/web/middleware/redis/RedisExploitationTools.vue')
const RedisHardening = () => import('./pages/web/middleware/redis/RedisHardening.vue')

// Elasticsearch middleware pages
const ElasticsearchConfig = () => import('./pages/web/middleware/elasticsearch/ElasticsearchConfig.vue')
const ElasticsearchVulnerabilities = () => import('./pages/web/middleware/elasticsearch/ElasticsearchVulnerabilities.vue')
const ElasticsearchHardening = () => import('./pages/web/middleware/elasticsearch/ElasticsearchHardening.vue')

// Jenkins middleware pages
const JenkinsCompleteConfig = () => import('./pages/web/middleware/jenkins/CompleteConfig.vue')
const JenkinsVulnerabilitiesDatabase = () => import('./pages/web/middleware/jenkins/VulnerabilitiesDatabase.vue')
const JenkinsSecurityHardening = () => import('./pages/web/middleware/jenkins/SecurityHardening.vue')

// Framework Security pages
const Framework = () => import('./pages/web/framework/Framework.vue')
const JavaFramework = () => import('./pages/web/framework/java/JavaFramework.vue')
const PHPFramework = () => import('./pages/web/framework/php/PHPFramework.vue')
const PythonFramework = () => import('./pages/web/framework/python/PythonFramework.vue')
const NodejsFramework = () => import('./pages/web/framework/nodejs/NodejsFramework.vue')
const DotnetFramework = () => import('./pages/web/framework/dotnet/DotnetFramework.vue')

// Java Framework pages
const SpringFramework = () => import('./pages/web/framework/java/spring/SpringFramework.vue')
const SpringBasics = () => import('./pages/web/framework/java/spring/SpringBasics.vue')
const SpringConfig = () => import('./pages/web/framework/java/spring/SpringConfig.vue')
const SpringVulnerabilities = () => import('./pages/web/framework/java/spring/SpringVulnerabilities.vue')
const SpringHardening = () => import('./pages/web/framework/java/spring/SpringHardening.vue')

const StrutsFramework = () => import('./pages/web/framework/java/struts/StrutsFramework.vue')
const StrutsBasics = () => import('./pages/web/framework/java/struts/StrutsBasics.vue')
const StrutsConfig = () => import('./pages/web/framework/java/struts/StrutsConfig.vue')
const StrutsVulnerabilities = () => import('./pages/web/framework/java/struts/StrutsVulnerabilities.vue')
const StrutsHardening = () => import('./pages/web/framework/java/struts/StrutsHardening.vue')

const MyBatisFramework = () => import('./pages/web/framework/java/mybatis/MyBatisFramework.vue')
const MyBatisBasics = () => import('./pages/web/framework/java/mybatis/MyBatisBasics.vue')
const MyBatisConfig = () => import('./pages/web/framework/java/mybatis/MyBatisConfig.vue')
const MyBatisVulnerabilities = () => import('./pages/web/framework/java/mybatis/MyBatisVulnerabilities.vue')
const MyBatisHardening = () => import('./pages/web/framework/java/mybatis/MyBatisHardening.vue')

const ShiroFramework = () => import('./pages/web/framework/java/shiro/ShiroFramework.vue')
const ShiroBasics = () => import('./pages/web/framework/java/shiro/ShiroBasics.vue')
const ShiroConfig = () => import('./pages/web/framework/java/shiro/ShiroConfig.vue')
const ShiroVulnerabilities = () => import('./pages/web/framework/java/shiro/ShiroVulnerabilities.vue')
const ShiroHardening = () => import('./pages/web/framework/java/shiro/ShiroHardening.vue')

const HibernateFramework = () => import('./pages/web/framework/java/hibernate/HibernateFramework.vue')
const HibernateBasics = () => import('./pages/web/framework/java/hibernate/HibernateBasics.vue')
const HibernateConfig = () => import('./pages/web/framework/java/hibernate/HibernateConfig.vue')
const HibernateVulnerabilities = () => import('./pages/web/framework/java/hibernate/HibernateVulnerabilities.vue')
const HibernateHardening = () => import('./pages/web/framework/java/hibernate/HibernateHardening.vue')

const JSFFramework = () => import('./pages/web/framework/java/jsf/JSFFramework.vue')
const JSFBasics = () => import('./pages/web/framework/java/jsf/JSFBasics.vue')
const JSFConfig = () => import('./pages/web/framework/java/jsf/JSFConfig.vue')
const JSFVulnerabilities = () => import('./pages/web/framework/java/jsf/JSFVulnerabilities.vue')
const JSFHardening = () => import('./pages/web/framework/java/jsf/JSFHardening.vue')

const LaravelFramework = () => import('./pages/web/framework/php/laravel/LaravelFramework.vue')
const LaravelBasics = () => import('./pages/web/framework/php/laravel/LaravelBasics.vue')
const LaravelConfig = () => import('./pages/web/framework/php/laravel/LaravelConfig.vue')
const LaravelVulnerabilities = () => import('./pages/web/framework/php/laravel/LaravelVulnerabilities.vue')
const LaravelHardening = () => import('./pages/web/framework/php/laravel/LaravelHardening.vue')

// ThinkPHP Framework
const ThinkPHPFramework = () => import('./pages/web/framework/php/thinkphp/ThinkPHPFramework.vue')
const ThinkPHPBasics = () => import('./pages/web/framework/php/thinkphp/ThinkPHPBasics.vue')
const ThinkPHPConfig = () => import('./pages/web/framework/php/thinkphp/ThinkPHPConfig.vue')
const ThinkPHPVulnerabilities = () => import('./pages/web/framework/php/thinkphp/ThinkPHPVulnerabilities.vue')
const ThinkPHPHardening = () => import('./pages/web/framework/php/thinkphp/ThinkPHPHardening.vue')

// Yii Framework
const YiiFramework = () => import('./pages/web/framework/php/yii/YiiFramework.vue')
const YiiBasics = () => import('./pages/web/framework/php/yii/YiiBasics.vue')
const YiiConfig = () => import('./pages/web/framework/php/yii/YiiConfig.vue')
const YiiVulnerabilities = () => import('./pages/web/framework/php/yii/YiiVulnerabilities.vue')
const YiiHardening = () => import('./pages/web/framework/php/yii/YiiHardening.vue')

// Django Framework
const DjangoFramework = () => import('./pages/web/framework/python/django/DjangoFramework.vue')
const DjangoBasics = () => import('./pages/web/framework/python/django/DjangoBasics.vue')
const DjangoConfig = () => import('./pages/web/framework/python/django/DjangoConfig.vue')
const DjangoVulnerabilities = () => import('./pages/web/framework/python/django/DjangoVulnerabilities.vue')
const DjangoHardening = () => import('./pages/web/framework/python/django/DjangoHardening.vue')

// Flask Framework
const FlaskFramework = () => import('./pages/web/framework/python/flask/FlaskFramework.vue')
const FlaskBasics = () => import('./pages/web/framework/python/flask/FlaskBasics.vue')
const FlaskConfig = () => import('./pages/web/framework/python/flask/FlaskConfig.vue')
const FlaskVulnerabilities = () => import('./pages/web/framework/python/flask/FlaskVulnerabilities.vue')
const FlaskHardening = () => import('./pages/web/framework/python/flask/FlaskHardening.vue')

// FastAPI Framework
const FastAPIFramework = () => import('./pages/web/framework/python/fastapi/FastAPIFramework.vue')
const FastAPIBasics = () => import('./pages/web/framework/python/fastapi/FastAPIBasics.vue')
const FastAPIConfig = () => import('./pages/web/framework/python/fastapi/FastAPIConfig.vue')
const FastAPIVulnerabilities = () => import('./pages/web/framework/python/fastapi/FastAPIVulnerabilities.vue')
const FastAPIHardening = () => import('./pages/web/framework/python/fastapi/FastAPIHardening.vue')


const routes = [
  { path: '/', component: CTFHome },
  { path: '/old-home', component: Home },
  { path: '/web', component: Web, name: 'WebSecurity' },
  { path: '/web/middleware', component: Middleware, name: 'MiddlewareSecurity' },
  { path: '/web/middleware/nginx/complete-config', component: NginxCompleteConfig, name: 'NginxCompleteConfig' },
  { path: '/web/middleware/nginx/vulnerabilities', component: NginxVulnerabilitiesDatabase, name: 'NginxVulnerabilities' },
  { path: '/web/middleware/nginx/security-hardening', component: NginxSecurityHardening, name: 'NginxSecurityHardening' },
  { path: '/web/middleware/nginx/nginxpwner-manual', component: NginxpwnerManual, name: 'NginxpwnerManual' },

  // Apache routes
  { path: '/web/middleware/apache/complete-config', component: ApacheCompleteConfig, name: 'ApacheCompleteConfig' },
  { path: '/web/middleware/apache/vulnerabilities', component: ApacheVulnerabilitiesDatabase, name: 'ApacheVulnerabilitiesDatabase' },
  { path: '/web/middleware/apache/security-hardening', component: ApacheSecurityHardening, name: 'ApacheSecurityHardening' },

  // IIS routes
  { path: '/web/middleware/iis/complete-config', component: IISCompleteConfig, name: 'IISCompleteConfig' },
  { path: '/web/middleware/iis/vulnerabilities', component: IISVulnerabilitiesDatabase, name: 'IISVulnerabilitiesDatabase' },
  { path: '/web/middleware/iis/security-hardening', component: IISSecurityHardening, name: 'IISSecurityHardening' },

  // Tomcat routes
  { path: '/web/middleware/tomcat/complete-config', component: TomcatCompleteConfig, name: 'TomcatCompleteConfig' },
  { path: '/web/middleware/tomcat/vulnerabilities', component: TomcatVulnerabilitiesDatabase, name: 'TomcatVulnerabilitiesDatabase' },
  { path: '/web/middleware/tomcat/security-hardening', component: TomcatSecurityHardening, name: 'TomcatSecurityHardening' },
  { path: '/web/middleware/tomcat/security-hardening-main', component: TomcatSecurityHardeningMain, name: 'TomcatSecurityHardeningMain' },

  // JBoss routes
  { path: '/web/middleware/jboss/complete-config', component: JBossCompleteConfig, name: 'JBossCompleteConfig' },
  { path: '/web/middleware/jboss/vulnerabilities', component: JBossVulnerabilitiesDatabase, name: 'JBossVulnerabilitiesDatabase' },
  { path: '/web/middleware/jboss/security-hardening', component: JBossSecurityHardening, name: 'JBossSecurityHardening' },

  // WebLogic routes
  { path: '/web/middleware/weblogic/complete-config', component: WebLogicCompleteConfig, name: 'WebLogicCompleteConfig' },
  { path: '/web/middleware/weblogic/vulnerabilities', component: WebLogicVulnerabilitiesDatabase, name: 'WebLogicVulnerabilitiesDatabase' },
  { path: '/web/middleware/weblogic/security-hardening', component: WebLogicSecurityHardening, name: 'WebLogicSecurityHardening' },
  { path: '/web/java', component: JavaBase, name: 'JavaBase' },
  { path: '/web/java/reflection', component: Reflection, name: 'JavaReflection' },
  { path: '/web/java/reflection-vuln', component: ReflectionVuln, name: 'JavaReflectionVuln' },
  { path: '/web/java/proxy', component: Proxy, name: 'JavaProxy' },
  { path: '/web/java/classloader', component: ClassLoader, name: 'JavaClassLoader' },
  { path: '/web/java/serialization', component: Serialization, name: 'JavaSerialization' },
  { path: '/web/java/DeserializationRisk', component: DeserializationRisk, name: 'JavaDeserializationRisk' },
  { path: '/web/java/chain/urldns', component: URLDNS, name: 'JavaChainURLDNS' },
  { path: '/web/java/chain/jdk7u21', component: JDK7u21, name: 'JavaChainJDK7u21' },
  { path: '/web/java/chain/jdk8u20', component: JDK8u20, name: 'JavaChainJDK8u20' },
  { path: '/web/java/chain/jrmpclient', component: JRMPClient, name: 'JavaChainJRMPClient' },
  { path: '/web/java/chain/jrmplistener', component: JRMPListener, name: 'JavaChainJRMPListener' },
  { path: '/web/java/chain/spring1', component: Spring1, name: 'JavaChainSpring1' },
  { path: '/web/java/chain/spring2', component: Spring2, name: 'JavaChainSpring2' },
  { path: '/web/java/chain/spring3', component: Spring3, name: 'JavaChainSpring3' },
  { path: '/web/java/chain/springboot', component: SpringBoot, name: 'JavaChainSpringBoot' },
  { path: '/web/java/chain/springcloud', component: SpringCloud, name: 'JavaChainSpringCloud' },
  { path: '/web/java/chain/springsecurity', component: SpringSecurity, name: 'JavaChainSpringSecurity' },
  { path: '/web/java/chain/cc1', component: CC1, name: 'JavaChainCC1' },
  { path: '/web/java/chain/cc2', component: CC2, name: 'JavaChainCC2' },
  { path: '/web/java/chain/cc3', component: CC3, name: 'JavaChainCC3' },
  { path: '/web/java/chain/cc4', component: CC4, name: 'JavaChainCC4' },
  { path: '/web/java/chain/cc5', component: CC5, name: 'JavaChainCC5' },
  { path: '/web/java/chain/cc6', component: CC6, name: 'JavaChainCC6' },
  { path: '/web/java/chain/cc7', component: CC7, name: 'JavaChainCC7' },
  { path: '/web/java/chain/cc11', component: CC11, name: 'JavaChainCC11' },
  { path: '/web/java/tools', component: JavaTools, name: 'JavaTools' },
  { path: '/web/java/tools/ysoserial', component: YsoserialTool, name: 'YsoserialTool' },
  { path: '/web/java/tools/jndiexploit', component: JNDIExploitTool, name: 'JNDIExploitTool' },
  { path: '/web/java/tools/marshalsec', component: MarshalsecTool, name: 'MarshalsecTool' },
  { path: '/web/java/tools/memshell', component: MemShellTool, name: 'MemShellTool' },
  { path: '/web/java/tools/jyso', component: JysoTool, name: 'JysoTool' },

  // Eclipse Jetty middleware routes
  { path: '/web/middleware/jetty/configuration', component: JettyCompleteConfig, name: 'JettyCompleteConfig' },
  { path: '/web/middleware/jetty/vulnerabilities', component: JettyVulnerabilitiesMain, name: 'JettyVulnerabilitiesMain' },
  { path: '/web/middleware/jetty/hardening', component: JettySecurityHardeningMain, name: 'JettySecurityHardeningMain' },

  // MySQL Proxy middleware routes
  { path: '/web/middleware/mysql-proxy/configuration', component: MySQLProxyCompleteConfig, name: 'MySQLProxyCompleteConfig' },
  { path: '/web/middleware/mysql-proxy/vulnerabilities', component: MySQLProxyVulnerabilitiesMain, name: 'MySQLProxyVulnerabilitiesMain' },
  { path: '/web/middleware/mysql-proxy/hardening', component: MySQLProxySecurityHardeningMain, name: 'MySQLProxySecurityHardeningMain' },

  // MyCat middleware routes
  { path: '/web/middleware/mycat/configuration', component: MyCatConfig, name: 'MyCatConfig' },
  { path: '/web/middleware/mycat/vulnerabilities', component: MyCatVulnerabilities, name: 'MyCatVulnerabilities' },
  { path: '/web/middleware/mycat/hardening', component: MyCatHardening, name: 'MyCatHardening' },

  // RabbitMQ middleware routes
  { path: '/web/middleware/rabbitmq/configuration', component: RabbitMQConfig, name: 'RabbitMQConfig' },
  { path: '/web/middleware/rabbitmq/vulnerabilities', component: RabbitMQVulnerabilities, name: 'RabbitMQVulnerabilities' },
  { path: '/web/middleware/rabbitmq/hardening', component: RabbitMQHardening, name: 'RabbitMQHardening' },

  // ActiveMQ middleware routes
  { path: '/web/middleware/activemq/configuration', component: ActiveMQConfig, name: 'ActiveMQConfig' },
  { path: '/web/middleware/activemq/vulnerabilities', component: ActiveMQVulnerabilities, name: 'ActiveMQVulnerabilities' },
  { path: '/web/middleware/activemq/hardening', component: ActiveMQHardening, name: 'ActiveMQHardening' },

  // Kafka middleware routes
  { path: '/web/middleware/kafka/configuration', component: KafkaConfig, name: 'KafkaConfig' },
  { path: '/web/middleware/kafka/vulnerabilities', component: KafkaVulnerabilities, name: 'KafkaVulnerabilities' },
  { path: '/web/middleware/kafka/hardening', component: KafkaHardening, name: 'KafkaHardening' },

  // Redis middleware routes
  { path: '/web/middleware/redis/configuration', component: RedisConfig, name: 'RedisConfig' },
  { path: '/web/middleware/redis/vulnerabilities', component: RedisVulnerabilities, name: 'RedisVulnerabilities' },
  { path: '/web/middleware/redis/exploitation-tools', component: RedisExploitationTools, name: 'RedisExploitationTools' },
  { path: '/web/middleware/redis/security-hardening', component: RedisHardening, name: 'RedisHardening' },

  // Elasticsearch middleware routes
  { path: '/web/middleware/elasticsearch/configuration', component: ElasticsearchConfig, name: 'ElasticsearchConfig' },
  { path: '/web/middleware/elasticsearch/vulnerabilities', component: ElasticsearchVulnerabilities, name: 'ElasticsearchVulnerabilities' },
  { path: '/web/middleware/elasticsearch/security-hardening', component: ElasticsearchHardening, name: 'ElasticsearchHardening' },

  // Jenkins middleware routes
  { path: '/web/middleware/jenkins/complete-config', component: JenkinsCompleteConfig, name: 'JenkinsCompleteConfig' },
  { path: '/web/middleware/jenkins/security-hardening', component: JenkinsSecurityHardening, name: 'JenkinsSecurityHardening' },
  { path: '/web/middleware/jenkins/vulnerabilities', component: JenkinsVulnerabilitiesDatabase, name: 'JenkinsVulnerabilitiesDatabase' },

  // Framework Security routes
  { path: '/web/framework', component: Framework, name: 'Framework' },
  { path: '/web/framework/java', component: JavaFramework, name: 'JavaFramework' },
  { path: '/web/framework/php', component: PHPFramework, name: 'PHPFramework' },
  { path: '/web/framework/python', component: PythonFramework, name: 'PythonFramework' },
  { path: '/web/framework/nodejs', component: NodejsFramework, name: 'NodejsFramework' },
  { path: '/web/framework/dotnet', component: DotnetFramework, name: 'DotnetFramework' },

  // Java Framework routes
  { path: '/web/framework/java/spring', component: SpringFramework, name: 'SpringFramework' },
  { path: '/web/framework/java/spring/basics', component: SpringBasics, name: 'SpringBasics' },
  { path: '/web/framework/java/spring/config', component: SpringConfig, name: 'SpringConfig' },
  { path: '/web/framework/java/spring/vulnerabilities', component: SpringVulnerabilities, name: 'SpringVulnerabilities' },
  { path: '/web/framework/java/spring/hardening', component: SpringHardening, name: 'SpringHardening' },

  { path: '/web/framework/java/struts', component: StrutsFramework, name: 'StrutsFramework' },
  { path: '/web/framework/java/struts/basics', component: StrutsBasics, name: 'StrutsBasics' },
  { path: '/web/framework/java/struts/config', component: StrutsConfig, name: 'StrutsConfig' },
  { path: '/web/framework/java/struts/vulnerabilities', component: StrutsVulnerabilities, name: 'StrutsVulnerabilities' },
  { path: '/web/framework/java/struts/hardening', component: StrutsHardening, name: 'StrutsHardening' },

  { path: '/web/framework/java/mybatis', component: MyBatisFramework, name: 'MyBatisFramework' },
  { path: '/web/framework/java/mybatis/basics', component: MyBatisBasics, name: 'MyBatisBasics' },
  { path: '/web/framework/java/mybatis/config', component: MyBatisConfig, name: 'MyBatisConfig' },
  { path: '/web/framework/java/mybatis/vulnerabilities', component: MyBatisVulnerabilities, name: 'MyBatisVulnerabilities' },
  { path: '/web/framework/java/mybatis/hardening', component: MyBatisHardening, name: 'MyBatisHardening' },

  { path: '/web/framework/java/shiro', component: ShiroFramework, name: 'ShiroFramework' },
  { path: '/web/framework/java/shiro/basics', component: ShiroBasics, name: 'ShiroBasics' },
  { path: '/web/framework/java/shiro/config', component: ShiroConfig, name: 'ShiroConfig' },
  { path: '/web/framework/java/shiro/vulnerabilities', component: ShiroVulnerabilities, name: 'ShiroVulnerabilities' },
  { path: '/web/framework/java/shiro/hardening', component: ShiroHardening, name: 'ShiroHardening' },

  { path: '/web/framework/java/hibernate', component: HibernateFramework, name: 'HibernateFramework' },
  { path: '/web/framework/java/hibernate/basics', component: HibernateBasics, name: 'HibernateBasics' },
  { path: '/web/framework/java/hibernate/config', component: HibernateConfig, name: 'HibernateConfig' },
  { path: '/web/framework/java/hibernate/vulnerabilities', component: HibernateVulnerabilities, name: 'HibernateVulnerabilities' },
  { path: '/web/framework/java/hibernate/hardening', component: HibernateHardening, name: 'HibernateHardening' },

  { path: '/web/framework/java/jsf', component: JSFFramework, name: 'JSFFramework' },
  { path: '/web/framework/java/jsf/basics', component: JSFBasics, name: 'JSFBasics' },
  { path: '/web/framework/java/jsf/config', component: JSFConfig, name: 'JSFConfig' },
  { path: '/web/framework/java/jsf/vulnerabilities', component: JSFVulnerabilities, name: 'JSFVulnerabilities' },
  { path: '/web/framework/java/jsf/hardening', component: JSFHardening, name: 'JSFHardening' },

  { path: '/web/framework/php/laravel', component: LaravelFramework, name: 'LaravelFramework' },
  { path: '/web/framework/php/laravel/basics', component: LaravelBasics, name: 'LaravelBasics' },
  { path: '/web/framework/php/laravel/config', component: LaravelConfig, name: 'LaravelConfig' },
  { path: '/web/framework/php/laravel/vulnerabilities', component: LaravelVulnerabilities, name: 'LaravelVulnerabilities' },
  { path: '/web/framework/php/laravel/hardening', component: LaravelHardening, name: 'LaravelHardening' },

  // ThinkPHP Framework Routes
  { path: '/web/framework/php/thinkphp', component: ThinkPHPFramework, name: 'ThinkPHPFramework' },
  { path: '/web/framework/php/thinkphp/basics', component: ThinkPHPBasics, name: 'ThinkPHPBasics' },
  { path: '/web/framework/php/thinkphp/config', component: ThinkPHPConfig, name: 'ThinkPHPConfig' },
  { path: '/web/framework/php/thinkphp/vulnerabilities', component: ThinkPHPVulnerabilities, name: 'ThinkPHPVulnerabilities' },
  { path: '/web/framework/php/thinkphp/hardening', component: ThinkPHPHardening, name: 'ThinkPHPHardening' },

  // Yii Framework Routes
  { path: '/web/framework/php/yii', component: YiiFramework, name: 'YiiFramework' },
  { path: '/web/framework/php/yii/basics', component: YiiBasics, name: 'YiiBasics' },
  { path: '/web/framework/php/yii/config', component: YiiConfig, name: 'YiiConfig' },
  { path: '/web/framework/php/yii/vulnerabilities', component: YiiVulnerabilities, name: 'YiiVulnerabilities' },
  { path: '/web/framework/php/yii/hardening', component: YiiHardening, name: 'YiiHardening' },

  // Django Framework Routes
  { path: '/web/framework/python/django', component: DjangoFramework, name: 'DjangoFramework' },
  { path: '/web/framework/python/django/basics', component: DjangoBasics, name: 'DjangoBasics' },
  { path: '/web/framework/python/django/config', component: DjangoConfig, name: 'DjangoConfig' },
  { path: '/web/framework/python/django/vulnerabilities', component: DjangoVulnerabilities, name: 'DjangoVulnerabilities' },
  { path: '/web/framework/python/django/hardening', component: DjangoHardening, name: 'DjangoHardening' },

  // Flask Framework Routes
  { path: '/web/framework/python/flask', component: FlaskFramework, name: 'FlaskFramework' },
  { path: '/web/framework/python/flask/basics', component: FlaskBasics, name: 'FlaskBasics' },
  { path: '/web/framework/python/flask/config', component: FlaskConfig, name: 'FlaskConfig' },
  { path: '/web/framework/python/flask/vulnerabilities', component: FlaskVulnerabilities, name: 'FlaskVulnerabilities' },
  { path: '/web/framework/python/flask/hardening', component: FlaskHardening, name: 'FlaskHardening' },

  // FastAPI Framework Routes
  { path: '/web/framework/python/fastapi', component: FastAPIFramework, name: 'FastAPIFramework' },
  { path: '/web/framework/python/fastapi/basics', component: FastAPIBasics, name: 'FastAPIBasics' },
  { path: '/web/framework/python/fastapi/config', component: FastAPIConfig, name: 'FastAPIConfig' },
  { path: '/web/framework/python/fastapi/vulnerabilities', component: FastAPIVulnerabilities, name: 'FastAPIVulnerabilities' },
  { path: '/web/framework/python/fastapi/hardening', component: FastAPIHardening, name: 'FastAPIHardening' },
]

const router = createRouter({
  history: createWebHistory(),
  routes,
})

export default router
